
/**
 * 🔧 ОТЛАДОЧНЫЙ СКРИПТ ДЛЯ РУЧНОЙ ИНСТРУКЦИИ ДОБАВЛЕНИЯ ЛИКВИДНОСТИ
 *
 * ТОЛЬКО РУЧНЫЕ МЕТОДЫ БЕЗ SDK - ОЧИЩЕНО ОТ МУСОРА
 */

// Загрузка переменных окружения
require('dotenv').config();

const { Connection, Keypair, PublicKey, TransactionInstruction } = require('@solana/web3.js');
const { TOKEN_PROGRAM_ID, getAssociatedTokenAddress } = require('@solana/spl-token');
const { BN } = require('@coral-xyz/anchor');

class AddLiquidityDebugger {
    constructor() {
        // 🔥 КОНСТАНТЫ ИЗ БЭКАПА (ТОЧНАЯ КОПИЯ)
        this.METEORA_PROGRAM_ID = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        this.METEORA_DLMM_PROGRAM = this.METEORA_PROGRAM_ID; // Алиас
        this.TOKEN_PROGRAM = TOKEN_PROGRAM_ID;
        this.CHUNK_SIZE = 70; // 70 бинов в каждом bin array
        this.BIN_ARRAY_SIZE = this.CHUNK_SIZE; // Алиас для совместимости

        console.log(`✅ METEORA_DLMM_PROGRAM: ${this.METEORA_DLMM_PROGRAM.toString().slice(0,8)}...`);
        console.log(`✅ TOKEN_PROGRAM: ${this.TOKEN_PROGRAM.toString().slice(0,8)}...`);
    }

    // 🔥 РУЧНАЯ ИНСТРУКЦИЯ add_liquidity_by_strategy2 БЕЗ SDK! (ТОЧНАЯ КОПИЯ ИЗ БЭКАПА)
    async createManualAddLiquidityByStrategy2(params) {
        const {
            positionPubKey,
            user,
            totalXAmount,
            totalYAmount,
            activeBinId,
            minBinId,
            maxBinId,
            remainingAccountsInfo,
            dlmm,
            userTokenX,
            userTokenY
        } = params;

        console.log(`🔥 СОЗДАНИЕ РУЧНОЙ ИНСТРУКЦИИ add_liquidity2 (ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ):`);
        console.log(`   Position: ${positionPubKey.toString().slice(0,8)}...`);
        console.log(`   User: ${user.toString().slice(0,8)}...`);
        console.log(`   Amount X: ${totalXAmount.toString()}`);
        console.log(`   Amount Y: ${totalYAmount.toString()}`);
        console.log(`   Active Bin: ${activeBinId}`);
        console.log(`   Min Bin: ${minBinId}, Max Bin: ${maxBinId}`);

        // 🔥 ПРАВИЛЬНЫЙ DISCRIMINATOR ДЛЯ add_liquidity2 (ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ!)
        // Взят из реальной успешной транзакции: e4a24e1c46db7473
        const discriminator = Buffer.from([0xe4, 0xa2, 0x4e, 0x1c, 0x46, 0xdb, 0x74, 0x73]);

        // 🔥 СОЗДАЕМ INSTRUCTION DATA ДЛЯ add_liquidity_by_strategy2
        const instructionData = this.encodeAddLiquidityByStrategy2Data({
            amountX: totalXAmount,
            amountY: totalYAmount,
            activeBinId: activeBinId,
            minBinId: minBinId,
            maxBinId: maxBinId,
            remainingAccountsInfo: remainingAccountsInfo
        });

        // 🔥 СОЗДАЕМ СПИСОК АККАУНТОВ
        const keys = await this.buildAddLiquidityByStrategy2Keys({
            positionPubKey,
            user,
            dlmm,
            userTokenX,
            userTokenY,
            remainingAccountsInfo
        });

        console.log(`✅ РУЧНАЯ ИНСТРУКЦИЯ СОЗДАНА: ${keys.length} аккаунтов, ${instructionData.length} байт данных`);

        return new TransactionInstruction({
            programId: this.METEORA_DLMM_PROGRAM,
            keys: keys,
            data: instructionData
        });
    }

    // 🔥 КОДИРОВАНИЕ ДАННЫХ ДЛЯ add_liquidity_by_strategy2
    encodeAddLiquidityByStrategy2Data(params) {
        const { amountX, amountY, activeBinId, minBinId, maxBinId, remainingAccountsInfo } = params;

        // 🔥 ПРАВИЛЬНЫЙ DISCRIMINATOR ДЛЯ add_liquidity2 (ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ!)
        const discriminator = Buffer.from([0xe4, 0xa2, 0x4e, 0x1c, 0x46, 0xdb, 0x74, 0x73]);

        // 🔥 ДИНАМИЧЕСКОЕ ПОСТРОЕНИЕ LIQUIDITY PARAMETER (ИСПРАВЛЕНО ПО РЕКОМЕНДАЦИИ GPT!)
        // Рассчитываем точный размер: amount_x(8) + amount_y(8) + active_bin_id(4) + min_bin_id(4) + max_bin_id(4) + bin_count(4) + bins(3*12)
        const binIds = [minBinId, activeBinId, maxBinId];
        const binCount = binIds.length;
        const liquidityParamSize = 8 + 8 + 4 + 4 + 4 + 4 + (binCount * 12); // 12 байт на бин (4 + 8)
        console.log(`🔍 Рассчитанный размер liquidity parameter: ${liquidityParamSize} байт`);

        const liquidityParamBuffer = Buffer.alloc(liquidityParamSize);
        let offset = 0;

        // amount_x (8 байт, little endian)
        liquidityParamBuffer.writeBigUInt64LE(BigInt(amountX.toString()), offset);
        offset += 8;

        // amount_y (8 байт, little endian)
        liquidityParamBuffer.writeBigUInt64LE(BigInt(amountY.toString()), offset);
        offset += 8;

        // 🔥 ДОБАВЛЯЕМ ACTIVE_BIN_ID (КАК РЕКОМЕНДОВАЛ GPT!)
        liquidityParamBuffer.writeInt32LE(activeBinId, offset);
        offset += 4;
        console.log(`🔍 Добавлен active_bin_id: ${activeBinId} в offset ${offset - 4}`);

        // 🔥 ДОБАВЛЯЕМ MIN_BIN_ID И MAX_BIN_ID (ДЛЯ ПОЛНОТЫ СТРУКТУРЫ)
        liquidityParamBuffer.writeInt32LE(minBinId, offset);
        offset += 4;
        console.log(`🔍 Добавлен min_bin_id: ${minBinId} в offset ${offset - 4}`);

        liquidityParamBuffer.writeInt32LE(maxBinId, offset);
        offset += 4;
        console.log(`🔍 Добавлен max_bin_id: ${maxBinId} в offset ${offset - 4}`);

        // 🔥 BIN_LIQUIDITY_DIST (РАСПРЕДЕЛЕНИЕ ПО 3 БИНАМ!)
        console.log(`🔥 КОДИРУЕМ BIN_LIQUIDITY_DIST ДЛЯ 3 БИНОВ: ${minBinId}, ${activeBinId}, ${maxBinId}`);

        // Количество бинов (4 байта)
        liquidityParamBuffer.writeUInt32LE(binCount, offset);
        offset += 4;

        // Данные каждого бина (bin_id + liquidity_share)
        binIds.forEach((binId, index) => {
            // bin_id (4 байта, signed)
            liquidityParamBuffer.writeInt32LE(binId, offset);
            offset += 4;

            // liquidity_share (8 байт) - равномерное распределение
            const liquidityShare = Math.floor(10000 / binCount); // Базовые пункты (10000 = 100%)
            liquidityParamBuffer.writeBigUInt64LE(BigInt(liquidityShare), offset);
            offset += 8;

            console.log(`   Bin ${binId}: ${liquidityShare} базовых пунктов`);
        });

        console.log(`🔍 LIQUIDITY PARAMETER С BIN_LIQUIDITY_DIST: ${offset} байт`);

        // 🔥 REMAINING_ACCOUNTS_INFO
        const remainingAccountsBuffer = this.encodeRemainingAccountsInfo(remainingAccountsInfo);
        console.log(`🔍 REMAINING_ACCOUNTS РАЗМЕР: ${remainingAccountsBuffer.length} байт`);

        // 🔥 ОБЪЕДИНЯЕМ ВСЕ БУФЕРЫ
        const totalLength = discriminator.length + offset + remainingAccountsBuffer.length;
        console.log(`🔍 ОБЩИЙ РАЗМЕР: ${discriminator.length} + ${offset} + ${remainingAccountsBuffer.length} = ${totalLength} байт`);

        const finalBuffer = Buffer.alloc(totalLength);

        let finalOffset = 0;
        discriminator.copy(finalBuffer, finalOffset);
        finalOffset += discriminator.length;

        liquidityParamBuffer.copy(finalBuffer, finalOffset, 0, offset);
        finalOffset += offset;

        remainingAccountsBuffer.copy(finalBuffer, finalOffset);

        console.log(`✅ INSTRUCTION DATA ЗАКОДИРОВАН: ${finalBuffer.length} байт`);
        return finalBuffer;
    }

    // 🔥 КОДИРОВАНИЕ REMAINING_ACCOUNTS_INFO (ИСПРАВЛЕНО ПО РЕКОМЕНДАЦИИ GPT!)
    encodeRemainingAccountsInfo(remainingAccountsInfo) {
        if (!remainingAccountsInfo || !remainingAccountsInfo.slices) {
            // Пустой remaining_accounts_info
            const buffer = Buffer.alloc(4);
            buffer.writeUInt32LE(0, 0); // 0 slices
            return buffer;
        }

        const slices = remainingAccountsInfo.slices;

        // 🔥 ПРАВИЛЬНЫЙ РАСЧЕТ РАЗМЕРА БУФЕРА С ДЛИНАМИ SLICE!
        let totalAccounts = 0;
        slices.forEach(slice => {
            totalAccounts += slice.length; // Каждый slice - массив аккаунтов
        });

        console.log(`🔍 РАСЧЕТ РАЗМЕРА БУФЕРА: ${slices.length} slices, ${totalAccounts} аккаунтов`);

        // 4 байта для количества slices + 4 байта длины на каждый slice + по 34 байта на аккаунт
        const bufferSize = 4 + (slices.length * 4) + (totalAccounts * 34);
        console.log(`🔍 РАЗМЕР БУФЕРА: 4 + ${slices.length * 4} + ${totalAccounts * 34} = ${bufferSize} байт`);

        const buffer = Buffer.alloc(bufferSize);
        let offset = 0;

        // Количество slices
        buffer.writeUInt32LE(slices.length, offset);
        offset += 4;
        console.log(`🔍 Записано количество slices: ${slices.length} в offset ${offset - 4}`);

        // Каждый slice
        slices.forEach((slice, sliceIndex) => {
            console.log(`🔍 Обрабатываем slice ${sliceIndex}: ${slice.length} аккаунтов`);

            // 🔥 ИСПРАВЛЕНО: ДОБАВЛЯЕМ ДЛИНУ SLICE (КАК УКАЗАЛ GPT!)
            buffer.writeUInt32LE(slice.length, offset);
            offset += 4;
            console.log(`🔍 Записана длина slice ${sliceIndex}: ${slice.length} в offset ${offset - 4}`);

            // 🔥 ИСПРАВЛЕНО: slice - это массив аккаунтов, НЕ объект с полем accounts!
            slice.forEach((account, accountIndex) => {
                console.log(`🔍 Аккаунт ${accountIndex}: offset=${offset}, bufferSize=${bufferSize}`);

                if (offset + 34 > bufferSize) {
                    throw new Error(`❌ ПЕРЕПОЛНЕНИЕ БУФЕРА: offset=${offset}, нужно=${offset + 34}, размер=${bufferSize}`);
                }

                account.pubkey.toBuffer().copy(buffer, offset);
                offset += 32;

                // Флаги (writable, signer)
                buffer.writeUInt8(account.isWritable ? 1 : 0, offset);
                offset += 1;
                buffer.writeUInt8(account.isSigner ? 1 : 0, offset);
                offset += 1;
            });
        });

        console.log(`✅ REMAINING_ACCOUNTS_INFO ЗАКОДИРОВАН: ${offset} байт использовано из ${bufferSize}`);
        return buffer.slice(0, offset);
    }

    // 🔥 ПОСТРОЕНИЕ КЛЮЧЕЙ ДЛЯ add_liquidity_by_strategy2
    async buildAddLiquidityByStrategy2Keys(params) {
        const { positionPubKey, user, dlmm, userTokenX, userTokenY, remainingAccountsInfo } = params;

        console.log(`🔥 ПОСТРОЕНИЕ КЛЮЧЕЙ ДЛЯ add_liquidity_by_strategy2:`);

        // 🔥 ОСНОВНЫЕ АККАУНТЫ (ОБЯЗАТЕЛЬНЫЕ)
        const keys = [
            // 1. Position (Writable)
            { pubkey: positionPubKey, isSigner: false, isWritable: true },

            // 2. LB Pair (Writable)
            { pubkey: dlmm.pubkey, isSigner: false, isWritable: true },

            // 3. Bin Array Bitmap Extension (ГЕНЕРИРУЕМ КАК В ОСНОВНОМ КОДЕ!)
            { pubkey: await this.getBinArrayBitmapExtension(dlmm.pubkey), isSigner: false, isWritable: false },

            // 4. User Token X (Writable)
            { pubkey: userTokenX, isSigner: false, isWritable: true },

            // 5. User Token Y (Writable)
            { pubkey: userTokenY, isSigner: false, isWritable: true },

            // 6. Reserve X (Writable)
            { pubkey: dlmm.lbPair.reserveX, isSigner: false, isWritable: true },

            // 7. Reserve Y (Writable)
            { pubkey: dlmm.lbPair.reserveY, isSigner: false, isWritable: true },

            // 8. Token X Mint
            { pubkey: dlmm.lbPair.tokenXMint, isSigner: false, isWritable: false },

            // 9. Token Y Mint
            { pubkey: dlmm.lbPair.tokenYMint, isSigner: false, isWritable: false },

            // 10. Sender (Signer + Writable + Fee Payer)
            { pubkey: user, isSigner: true, isWritable: true },

            // 11. Token Program X (Program)
            { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },

            // 12. Token Program Y (Program)
            { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },

            // 13. Event Authority
            { pubkey: dlmm.lbPair.eventAuthority || this.METEORA_DLMM_PROGRAM, isSigner: false, isWritable: false },

            // 14. Meteora DLMM Program (Program)
            { pubkey: this.METEORA_DLMM_PROGRAM, isSigner: false, isWritable: false },
        ];

        // 🔥 ДОБАВЛЯЕМ REMAINING_ACCOUNTS ИЗ remainingAccountsInfo
        if (remainingAccountsInfo && remainingAccountsInfo.slices) {
            console.log(`   🔥 ДОБАВЛЯЕМ ${remainingAccountsInfo.slices.length} SLICES ИЗ REMAINING_ACCOUNTS:`);

            remainingAccountsInfo.slices.forEach((slice, sliceIndex) => {
                console.log(`      Slice ${sliceIndex}: ${slice.length} аккаунтов`);

                // 🔥 ИСПРАВЛЕНО: slice - это массив аккаунтов, НЕ объект с полем accounts!
                slice.forEach((account, accountIndex) => {
                    keys.push({
                        pubkey: account.pubkey,
                        isSigner: account.isSigner || false,
                        isWritable: account.isWritable || true // BinArray обычно writable
                    });

                    console.log(`         [${keys.length-1}] ${account.pubkey.toString().slice(0,8)}... (${account.isWritable ? 'W' : 'R'}${account.isSigner ? 'S' : ''})`);
                });
            });
        }

        console.log(`✅ ПОСТРОЕНО ${keys.length} КЛЮЧЕЙ ДЛЯ РУЧНОЙ ИНСТРУКЦИИ`);
        return keys;
    }

    /**
     * 🔥 РАСЧЕТ ВЕСА ИНСТРУКЦИИ В БАЙТАХ И COMPUTE UNITS С ALT СЖАТИЕМ
     */
    async calculateInstructionWeight(instruction) {
        console.log(`   🔍 РАСЧЕТ ВЕСА ИНСТРУКЦИИ С ALT СЖАТИЕМ:`);

        // 🔥 ЗАГРУЖАЕМ ALT ТАБЛИЦЫ
        const altMap = await this.loadALTTables();

        // 🔥 ПРОВЕРЯЕМ ALT СЖАТИЕ
        const compressionStats = this.checkALTCompression(instruction, altMap);

        // 🔥 ВЕС БЕЗ СЖАТИЯ
        let uncompressedBytes = 0;

        // Program ID (32 байта)
        const programIdBytes = 32;
        uncompressedBytes += programIdBytes;

        // Данные инструкции
        const dataBytes = instruction.data.length;
        uncompressedBytes += dataBytes;

        // Аккаунты БЕЗ сжатия (каждый аккаунт = 32 байта pubkey + 1 байт флаги)
        const uncompressedAccountBytes = instruction.keys.length * 33;
        uncompressedBytes += uncompressedAccountBytes;

        // Метаданные инструкции
        const metadataBytes = 16;
        uncompressedBytes += metadataBytes;

        // 🔥 ВЕС С ALT СЖАТИЕМ
        let compressedBytes = uncompressedBytes;

        // Сжимаемые аккаунты: 32 байта → 1 байт (экономия 31 байт на аккаунт)
        const compressibleCount = compressionStats.compressible.length;
        const altSavings = compressibleCount * 31;
        compressedBytes -= altSavings;

        // ALT таблицы добавляют небольшой overhead (8 байт на таблицу)
        const altOverhead = 2 * 8; // 2 ALT таблицы
        compressedBytes += altOverhead;

        console.log(`   📊 ВЕС БЕЗ СЖАТИЯ:`);
        console.log(`      Program ID: ${programIdBytes} байт`);
        console.log(`      Данные: ${dataBytes} байт`);
        console.log(`      Аккаунты (${instruction.keys.length}): ${uncompressedAccountBytes} байт`);
        console.log(`      Метаданные: ${metadataBytes} байт`);
        console.log(`      🔥 ИТОГО БЕЗ СЖАТИЯ: ${uncompressedBytes} байт`);

        console.log(`   🗜️ ВЕС С ALT СЖАТИЕМ:`);
        console.log(`      Сжимаемые аккаунты: ${compressibleCount} (экономия ${altSavings} байт)`);
        console.log(`      ALT overhead: ${altOverhead} байт`);
        console.log(`      🔥 ИТОГО С СЖАТИЕМ: ${compressedBytes} байт`);
        console.log(`      💾 ОБЩАЯ ЭКОНОМИЯ: ${uncompressedBytes - compressedBytes} байт`);

        // 🔥 ВЕС В COMPUTE UNITS (не изменяется от сжатия)
        let computeUnits = 0;

        // Базовый вес для Meteora add_liquidity
        const BASE_WEIGHT = 15000;
        computeUnits += BASE_WEIGHT;

        // Вес за каждый аккаунт
        const ACCOUNT_WEIGHT = 100;
        computeUnits += instruction.keys.length * ACCOUNT_WEIGHT;

        // Дополнительный вес за writable аккаунты
        const writableAccounts = instruction.keys.filter(key => key.isWritable).length;
        const WRITABLE_WEIGHT = 200;
        computeUnits += writableAccounts * WRITABLE_WEIGHT;

        // Дополнительный вес за signer аккаунты
        const signerAccounts = instruction.keys.filter(key => key.isSigner).length;
        const SIGNER_WEIGHT = 300;
        computeUnits += signerAccounts * SIGNER_WEIGHT;

        // Вес за размер данных
        const DATA_WEIGHT_PER_BYTE = 10;
        computeUnits += instruction.data.length * DATA_WEIGHT_PER_BYTE;

        console.log(`   ⚡ ВЕС В COMPUTE UNITS:`);
        console.log(`      Базовый вес: ${BASE_WEIGHT} CU`);
        console.log(`      Аккаунты (${instruction.keys.length}): ${instruction.keys.length * ACCOUNT_WEIGHT} CU`);
        console.log(`      Writable (${writableAccounts}): ${writableAccounts * WRITABLE_WEIGHT} CU`);
        console.log(`      Signers (${signerAccounts}): ${signerAccounts * SIGNER_WEIGHT} CU`);
        console.log(`      Данные (${instruction.data.length} байт): ${instruction.data.length * DATA_WEIGHT_PER_BYTE} CU`);
        console.log(`      🔥 ИТОГО: ${computeUnits} CU`);

        return {
            uncompressedBytes,
            compressedBytes,
            computeUnits,
            compressionStats,
            savings: uncompressedBytes - compressedBytes
        };
    }

    /**
     * 🔥 ГЕНЕРАЦИЯ BINARRAY PDA КАК В ОСНОВНОМ КОДЕ (getBinArrayPDAsFromBinIds)!
     */
    async generateBinArrayPDAs(poolAddress, activeBinId, minBinId, maxBinId) {
        console.log(`🔥 ГЕНЕРАЦИЯ BINARRAY PDA КАК В ОСНОВНОМ КОДЕ:`);
        console.log(`   Pool: ${poolAddress.toString().slice(0,8)}...`);
        console.log(`   Active Bin: ${activeBinId}`);
        console.log(`   Range: ${minBinId} - ${maxBinId}`);

        // 🔥 НАШИ 3 БИНА (КАК В ОСНОВНОМ КОДЕ!)
        const ourBins = [minBinId, activeBinId, maxBinId];
        console.log(`   🔥 НАШИ 3 БИНА: ${ourBins.join(', ')}`);

        // 🔥 ИСПОЛЬЗУЕМ МЕТОД ИЗ ОСНОВНОГО КОДА!
        return await this.getBinArrayPDAsFromBinIds(ourBins, poolAddress, this.METEORA_DLMM_PROGRAM);
    }

    /**
     * 🔥 МЕТОД ИЗ ОСНОВНОГО КОДА: getBinArrayPDAsFromBinIds
     */
    async getBinArrayPDAsFromBinIds(binIds, marketPubkey, programId) {
        const chunkIds = this.getUniqueChunkIds(binIds);

        console.log(`   🔍 Bin IDs: ${binIds.join(', ')}`);
        console.log(`   🔍 Chunk IDs: ${chunkIds.join(', ')}`);
        console.log(`   ✅ Математически корректно: ${chunkIds.length} chunk'ов (≤2)`);

        const accounts = [];

        for (const chunkId of chunkIds) {
            // 🔥 ИСПОЛЬЗУЕМ ПРАВИЛЬНЫЙ ФОРМАТ ИЗ ОСНОВНОГО КОДА (BigInt64LE)!
            const seedChunk = Buffer.alloc(8);
            seedChunk.writeBigInt64LE(BigInt(chunkId), 0);

            const [binArrayPDA] = PublicKey.findProgramAddressSync([
                Buffer.from('bin_array'),
                marketPubkey.toBuffer(),
                seedChunk
            ], programId);

            console.log(`   📦 Chunk ${chunkId} → BinArray PDA: ${binArrayPDA.toString().slice(0, 8)}...`);

            accounts.push({
                pubkey: binArrayPDA,
                isSigner: false,
                isWritable: true
            });
        }

        // 🔥 ЕСЛИ НУЖНО 2 BINARRAY, А У НАС ТОЛЬКО 1 - СОЗДАЕМ СОСЕДНИЙ!
        if (accounts.length === 1 && binIds.length === 3) {
            console.log(`   🔥 СОЗДАЕМ ДОПОЛНИТЕЛЬНЫЙ BINARRAY ДЛЯ СОСЕДНЕГО CHUNK'А...`);

            // Создаем соседний chunk (±1 от текущего)
            const currentChunk = chunkIds[0];
            const neighborChunk = currentChunk + 1; // Соседний chunk

            const neighborSeedChunk = Buffer.alloc(8);
            neighborSeedChunk.writeBigInt64LE(BigInt(neighborChunk), 0);

            const [neighborPDA] = PublicKey.findProgramAddressSync([
                Buffer.from('bin_array'),
                marketPubkey.toBuffer(),
                neighborSeedChunk
            ], programId);

            console.log(`   📦 Соседний Chunk ${neighborChunk} → BinArray PDA: ${neighborPDA.toString().slice(0, 8)}...`);

            accounts.push({
                pubkey: neighborPDA,
                isSigner: false,
                isWritable: true
            });
        }

        console.log(`   📊 Итого валидных BinArray: ${accounts.length}/${chunkIds.length + (accounts.length > chunkIds.length ? 1 : 0)}`);
        return accounts;
    }

    /**
     * 🔥 ПОЛУЧЕНИЕ УНИКАЛЬНЫХ CHUNK IDS (СКОПИРОВАНО ИЗ ОСНОВНОГО КОДА!)
     */
    getUniqueChunkIds(binIds) {
        // 🔥 ИСПОЛЬЗУЕМ ПРАВИЛЬНЫЙ CHUNK_SIZE ИЗ ОСНОВНОГО КОДА!
        const CHUNK_SIZE = 70; // Как в основном коде
        const chunkSet = new Set();
        for (const binId of binIds) {
            const chunkId = Math.floor(binId / CHUNK_SIZE);
            chunkSet.add(chunkId);
        }
        return Array.from(chunkSet).sort((a, b) => a - b);
    }

    /**
     * 🔥 ПОЛУЧЕНИЕ BIN ARRAY BITMAP EXTENSION (СКОПИРОВАНО ИЗ ОСНОВНОГО КОДА!)
     */
    async getBinArrayBitmapExtension(poolAddress) {
        console.log(`🔍 ГЕНЕРИРУЕМ BIN ARRAY BITMAP EXTENSION для ${poolAddress.toString().slice(0,8)}...`);

        // 🔥 ПРОБУЕМ РАЗНЫЕ SEEDS КАК В ОСНОВНОМ КОДЕ!
        const possibleSeeds = ["bitmap", "bin_array_bitmap", "bin_array_bitmap_extension"];

        for (const seed of possibleSeeds) {
            const [testPDA] = PublicKey.findProgramAddressSync(
                [
                    Buffer.from(seed),
                    poolAddress.toBuffer()
                ],
                this.METEORA_DLMM_PROGRAM
            );

            console.log(`   🔍 Тест seed "${seed}": ${testPDA.toString().slice(0,8)}...`);

            // В тесте не проверяем существование - используем первый найденный
            if (seed === "bin_array_bitmap_extension") {
                console.log(`   ✅ ИСПОЛЬЗУЕМ seed "${seed}"`);
                return testPDA;
            }
        }

        // Fallback - используем дефолтный
        const [binArrayBitmapExtension] = PublicKey.findProgramAddressSync(
            [
                Buffer.from("bin_array_bitmap_extension"),
                poolAddress.toBuffer()
            ],
            this.METEORA_DLMM_PROGRAM
        );

        console.log(`   ⚠️ Используем дефолтный seed: ${binArrayBitmapExtension.toString().slice(0,8)}...`);
        return binArrayBitmapExtension;
    }

    /**
     * 🔥 ПОЛУЧЕНИЕ РЕАЛЬНЫХ АДРЕСОВ РЕЗЕРВОВ ИЗ КЭША (КАК В ОСНОВНОМ КОДЕ!)
     */
    getPoolReservesFromCache(poolAddress) {
        // 🔥 ПРАВИЛЬНЫЕ АДРЕСА РЕЗЕРВОВ ИЗ complete-flash-loan-structure.js
        const knownReserves = {
            // Pool 1: 5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6
            '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6': {
                reserveX: new PublicKey('EYj9xKw6ZszwpyNibHY7JD5o3QgTVrSdcBp1fMJhrR9o'), // POOL_1_RESERVE_X
                reserveY: new PublicKey('CoaxzEh8p5YyGLcj36Eo3cUThVJxeKCs7qvLAGDYwBcz')  // POOL_1_RESERVE_Y
            },
            // Pool 2: BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y
            'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y': {
                reserveX: new PublicKey('DwZz4S1Z1LBXomzmncQRVKCYhjCqSAMQ6RPKbUAadr7H'), // POOL_2_RESERVE_X
                reserveY: new PublicKey('4N22J4vW2juHocTntJNmXywSonYjkndCwahjZ2cYLDgb')  // POOL_2_RESERVE_Y
            }
        };

        const reserves = knownReserves[poolAddress];
        if (!reserves) {
            throw new Error(`❌ Неизвестный пул: ${poolAddress}`);
        }

        console.log(`   ✅ Найдены reserves для пула ${poolAddress.slice(0,8)}...`);
        return reserves;
    }

    /**
     * 🔥 СОЗДАНИЕ CLAIMFEE2 ИНСТРУКЦИИ (ОТКЛЮЧЕННОЙ В ОСНОВНОМ КОДЕ)
     */
    async createClaimFee2Instruction(poolAddress, activeBinId, minBinId, maxBinId) {
        console.log(`🔥 СОЗДАНИЕ CLAIMFEE2 ИНСТРУКЦИИ:`);
        console.log(`   Pool: ${poolAddress.toString().slice(0,8)}...`);
        console.log(`   Active Bin: ${activeBinId}`);
        console.log(`   Range: ${minBinId} - ${maxBinId}`);

        // 🔥 ПРАВИЛЬНЫЙ DISCRIMINATOR ДЛЯ CLAIM FEE (ИЗ ОСНОВНОГО КОДА)
        const discriminator = Buffer.from([0x70, 0xbf, 0x65, 0xab, 0x1c, 0x90, 0x7f, 0xbb]);

        // 🔥 СОЗДАЕМ ДАННЫЕ КАК В ОСНОВНОМ КОДЕ
        const minBinBuffer = Buffer.alloc(4);
        minBinBuffer.writeInt32LE(minBinId, 0);

        const maxBinBuffer = Buffer.alloc(4);
        maxBinBuffer.writeInt32LE(maxBinId, 0);

        const data = Buffer.concat([
            discriminator,      // 8 байтов
            minBinBuffer,       // 4 байта (min_bin_id)
            maxBinBuffer        // 4 байта (max_bin_id)
        ]);

        // 🔥 РЕАЛЬНЫЕ АДРЕСА ИЗ ОСНОВНОГО КОДА!
        const positionPubkey = new PublicKey('Gbv33r6KGduHGWb8D5m4jhJMTqnGE4oaMun7ToHLr1UC'); // POOL_1 Position
        const userPublicKey = new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV'); // Наш кошелек

        // 🔥 ОСНОВНЫЕ АККАУНТЫ ДЛЯ CLAIMFEE2 (КАК В УСПЕШНОЙ ТРАНЗАКЦИИ!)
        const accounts = [
            // 1. LB Pair (writable) - ПЕРВЫЙ в успешной транзакции!
            { pubkey: poolAddress, isSigner: false, isWritable: true },

            // 2. Position (writable) - ВТОРОЙ в успешной транзакции!
            { pubkey: positionPubkey, isSigner: false, isWritable: true },

            // 3. Sender (User) - ТРЕТИЙ в успешной транзакции! (signer + writable)
            { pubkey: userPublicKey, isSigner: true, isWritable: true },

            // 4. Reserve X (writable) - РЕАЛЬНЫЙ АДРЕС POOL_1_RESERVE_X
            { pubkey: new PublicKey('EYj9xKw6ZszwpyNibHY7JD5o3QgTVrSdcBp1fMJhrR9o'), isSigner: false, isWritable: true },

            // 5. Reserve Y (writable) - РЕАЛЬНЫЙ АДРЕС POOL_1_RESERVE_Y
            { pubkey: new PublicKey('CoaxzEh8p5YyGLcj36Eo3cUThVJxeKCs7qvLAGDYwBcz'), isSigner: false, isWritable: true },

            // 6. User Token X (writable) - РЕАЛЬНЫЙ WSOL ATA
            { pubkey: await getAssociatedTokenAddress(new PublicKey('So********************************111111112'), userPublicKey), isSigner: false, isWritable: true },

            // 7. User Token Y (writable) - РЕАЛЬНЫЙ USDC ATA
            { pubkey: await getAssociatedTokenAddress(new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'), userPublicKey), isSigner: false, isWritable: true },

            // 8. Token X Mint
            { pubkey: new PublicKey('So********************************111111112'), isSigner: false, isWritable: false },

            // 9. Token Y Mint
            { pubkey: new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'), isSigner: false, isWritable: false },

            // 10. Token Program X
            { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },

            // 11. Token Program Y
            { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },

            // 12. Memo Program (как в успешной транзакции)
            { pubkey: new PublicKey('MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr'), isSigner: false, isWritable: false },

            // 13. Event Authority
            { pubkey: new PublicKey('D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6'), isSigner: false, isWritable: false },

            // 14. Program (Meteora DLMM)
            { pubkey: this.METEORA_DLMM_PROGRAM, isSigner: false, isWritable: false }
        ];

        // 🔥 ДОБАВЛЯЕМ BIN ARRAYS КАК REMAINING ACCOUNTS (КАК В УСПЕШНОЙ ТРАНЗАКЦИИ - 2 АККАУНТА)
        try {
            const binArrayPDAs = await this.generateBinArrayPDAs(poolAddress, activeBinId, minBinId, maxBinId);
            binArrayPDAs.forEach(binArray => {
                accounts.push({ pubkey: binArray.pubkey, isSigner: false, isWritable: true }); // writable как в успешной транзакции
            });
            console.log(`   ✅ Добавлено ${binArrayPDAs.length} BinArray аккаунтов`);
        } catch (error) {
            console.log(`   ⚠️ Ошибка генерации BinArray: ${error.message}`);
            // Добавляем тестовые BinArray
            accounts.push({ pubkey: new PublicKey('33333333333333333333333333333333'), isSigner: false, isWritable: true });
            accounts.push({ pubkey: new PublicKey('44444444444444444444444444444444'), isSigner: false, isWritable: true });
        }

        console.log(`✅ CLAIMFEE2 СОЗДАН: ${accounts.length} аккаунтов, ${data.length} байт данных`);

        return new TransactionInstruction({
            programId: this.METEORA_DLMM_PROGRAM,
            keys: accounts,
            data: data
        });
    }

    /**
     * 🔥 СОЗДАНИЕ REMAINING_ACCOUNTS_INFO С РЕАЛЬНЫМИ BINARRAY
     */
    createRemainingAccountsInfo(binArrayPDAs) {
        if (!binArrayPDAs || binArrayPDAs.length === 0) {
            return null;
        }

        return {
            slices: [binArrayPDAs] // Один slice с BinArray PDA
        };
    }

    /**
     * 🔥 ЗАГРУЗКА ALT ТАБЛИЦ ДЛЯ ПРОВЕРКИ СЖАТИЯ
     */
    async loadALTTables() {
        console.log(`🔥 ЗАГРУЗКА ALT ТАБЛИЦ ДЛЯ ПРОВЕРКИ СЖАТИЯ...`);

        // Наши ALT таблицы
        const altAddresses = [
            'HGmknUTUmeovMc9ryERNWG6UFZDFDVr9xrum3ZhyL4fC', // MarginFi ALT
            'FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe'  // Custom ALT
        ];

        // Загружаем из локального файла для быстрой проверки
        try {
            const fs = require('fs');
            const altData = JSON.parse(fs.readFileSync('custom-alt-data.json', 'utf8'));

            const addressToALTIndex = new Map();

            // MarginFi ALT
            if (altData.tables.marginfi1) {
                altData.tables.marginfi1.addresses.forEach((address, index) => {
                    addressToALTIndex.set(address, {
                        tableIndex: 0,
                        addressIndex: index,
                        tableName: 'MarginFi'
                    });
                });
                console.log(`   ✅ MarginFi ALT: ${altData.tables.marginfi1.addresses.length} адресов`);
            }

            // Custom ALT
            if (altData.tables.custom) {
                altData.tables.custom.addresses.forEach((address, index) => {
                    addressToALTIndex.set(address, {
                        tableIndex: 1,
                        addressIndex: index,
                        tableName: 'Custom'
                    });
                });
                console.log(`   ✅ Custom ALT: ${altData.tables.custom.addresses.length} адресов`);
            }

            console.log(`   📊 Всего адресов в ALT: ${addressToALTIndex.size}`);
            return addressToALTIndex;

        } catch (error) {
            console.log(`   ❌ Ошибка загрузки ALT: ${error.message}`);
            return new Map();
        }
    }

    /**
     * 🔥 ПРОВЕРКА КАКИЕ АККАУНТЫ МОЖНО СЖАТЬ
     */
    checkALTCompression(instruction, altMap) {
        console.log(`   🔍 ПРОВЕРКА ALT СЖАТИЯ:`);

        const compressionStats = {
            compressible: [],
            nonCompressible: [],
            dynamic: [],
            totalSavings: 0
        };

        instruction.keys.forEach((key, index) => {
            const addressStr = key.pubkey.toString();
            const shortAddr = addressStr.slice(0, 8) + '...';
            const description = this.getAccountDescription(key.pubkey, index, instruction);

            // Проверяем есть ли в ALT
            if (altMap.has(addressStr)) {
                const altInfo = altMap.get(addressStr);

                // Проверяем можно ли сжимать (не signer и не динамический)
                if (key.isSigner) {
                    compressionStats.nonCompressible.push({
                        index,
                        address: shortAddr,
                        description,
                        reason: 'signer',
                        altTable: altInfo.tableName
                    });
                } else if (this.isDynamicAddress(addressStr)) {
                    compressionStats.dynamic.push({
                        index,
                        address: shortAddr,
                        description,
                        reason: 'dynamic',
                        altTable: altInfo.tableName
                    });
                } else {
                    compressionStats.compressible.push({
                        index,
                        address: shortAddr,
                        description,
                        altTable: altInfo.tableName,
                        altIndex: altInfo.addressIndex
                    });
                    // Экономия: 32 байта (полный адрес) → 1 байт (ALT индекс)
                    compressionStats.totalSavings += 31;
                }
            } else {
                compressionStats.nonCompressible.push({
                    index,
                    address: shortAddr,
                    description,
                    reason: 'not_in_alt'
                });
            }
        });

        // Выводим статистику с описаниями
        console.log(`      ✅ Сжимаемые (${compressionStats.compressible.length}):`);
        compressionStats.compressible.forEach(item => {
            console.log(`         [${item.index}] ${item.address} - ${item.description} → ALT ${item.altTable}[${item.altIndex}]`);
        });

        console.log(`      ❌ Несжимаемые (${compressionStats.nonCompressible.length}):`);
        compressionStats.nonCompressible.forEach(item => {
            console.log(`         [${item.index}] ${item.address} - ${item.description} (${item.reason})`);
        });

        if (compressionStats.dynamic.length > 0) {
            console.log(`      🔄 Динамические (${compressionStats.dynamic.length}):`);
            compressionStats.dynamic.forEach(item => {
                console.log(`         [${item.index}] ${item.address} - ${item.description} (${item.reason})`);
            });
        }

        console.log(`      💾 Экономия: ${compressionStats.totalSavings} байт`);

        return compressionStats;
    }

    /**
     * 🔥 ПРОВЕРКА НА ДИНАМИЧЕСКИЙ АДРЕС
     */
    isDynamicAddress(address) {
        // Динамические адреса которые нельзя сжимать
        const dynamicPatterns = [
            // BinArray PDA (генерируются динамически)
            /^[1-9A-HJ-NP-Za-km-z]{44}$/ // Все BinArray PDA динамические
        ];

        // Известные динамические адреса
        const knownDynamic = [
            // Position аккаунты (создаются пользователем)
            'Gbv33r6KGduHGWb8D5m4jhJMTqnGE4oaMun7ToHLr1UC',
            // User ATA аккаунты (зависят от пользователя)
            '68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk',
            '3AWxcMzxsTeBQ6YC1khJ9NkPtmLw2XbCGxvZHZpbAQFo'
        ];

        return knownDynamic.includes(address);
    }

    /**
     * 🔥 ОПРЕДЕЛЕНИЕ ТИПА АККАУНТА ПО АДРЕСУ
     */
    getAccountDescription(pubkey, index, instruction) {
        const address = pubkey.toString();

        // Определяем тип по позиции в инструкции add_liquidity_by_strategy2
        const accountTypes = [
            'Position',                    // 0 - Position account (writable)
            'LB Pair',                     // 1 - DLMM Pool (writable)
            'Bin Array Bitmap Extension',  // 2 - Bitmap extension
            'User Token X (WSOL ATA)',     // 3 - User WSOL ATA (writable)
            'User Token Y (USDC ATA)',     // 4 - User USDC ATA (writable)
            'Reserve X (WSOL)',            // 5 - Pool WSOL reserve (writable)
            'Reserve Y (USDC)',            // 6 - Pool USDC reserve (writable)
            'Token X Mint (WSOL)',         // 7 - WSOL mint
            'Token Y Mint (USDC)',         // 8 - USDC mint
            'User (Signer)',               // 9 - User wallet (signer + writable)
            'Token Program X',             // 10 - Token program for X
            'Token Program Y',             // 11 - Token program for Y
            'Event Authority',             // 12 - Event authority
            'Meteora DLMM Program',        // 13 - Meteora program
        ];

        // Базовое описание по индексу
        let description = accountTypes[index] || `Unknown Account ${index}`;

        // Дополнительные проверки по адресу
        if (address === 'So********************************111111112') {
            description = 'Token X Mint (WSOL)';
        } else if (address === 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v') {
            description = 'Token Y Mint (USDC)';
        } else if (address === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
            description = 'Token Program (SPL Token)';
        } else if (address === 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo') {
            description = 'Meteora DLMM Program';
        } else if (address === '********************************') {
            description = 'System Program';
        } else if (address === 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV') {
            description = 'User Wallet (Signer)';
        } else if (index >= 14) {
            // BinArray PDA (динамически добавленные)
            const binArrayNumber = index - 13;
            const boundaryName = binArrayNumber === 1 ? 'НИЖНЯЯ ГРАНИЦА' : 'ВЕРХНЯЯ ГРАНИЦА';
            description = `BinArray PDA #${binArrayNumber} (${boundaryName})`;
        }

        // Добавляем флаги
        const key = instruction.keys[index];
        const flags = [];
        if (key.isSigner) flags.push('signer');
        if (key.isWritable) flags.push('writable');

        const flagsStr = flags.length > 0 ? ` (${flags.join(' ')})` : '';

        return `${description}${flagsStr}`;
    }

    /**
     * 🔥 ОПРЕДЕЛЕНИЕ ТИПА АККАУНТА ДЛЯ CLAIMFEE2
     */
    getClaimFeeAccountDescription(pubkey, index, instruction) {
        const address = pubkey.toString();

        // Определяем тип по позиции в инструкции claim_fee (КАК В УСПЕШНОЙ ТРАНЗАКЦИИ!)
        const accountTypes = [
            'LB Pair',                     // 0 - DLMM Pool (writable) - ПЕРВЫЙ!
            'Position',                    // 1 - Position account (writable) - ВТОРОЙ!
            'Sender (User)',               // 2 - User wallet (signer + writable) - ТРЕТИЙ!
            'Reserve X (WSOL)',            // 3 - Pool WSOL reserve (writable)
            'Reserve Y (USDC)',            // 4 - Pool USDC reserve (writable)
            'User Token X (WSOL ATA)',     // 5 - User WSOL ATA (writable)
            'User Token Y (USDC ATA)',     // 6 - User USDC ATA (writable)
            'Token X Mint (WSOL)',         // 7 - WSOL mint
            'Token Y Mint (USDC)',         // 8 - USDC mint
            'Token Program X',             // 9 - Token program for X
            'Token Program Y',             // 10 - Token program for Y
            'Memo Program',                // 11 - Memo program
            'Event Authority',             // 12 - Event authority
            'Meteora DLMM Program',        // 13 - Meteora program
        ];

        // Базовое описание по индексу
        let description = accountTypes[index] || `Unknown Account ${index}`;

        // Дополнительные проверки по адресу
        if (address === 'So********************************111111112') {
            description = 'Token X Mint (WSOL)';
        } else if (address === 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v') {
            description = 'Token Y Mint (USDC)';
        } else if (address === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
            description = 'Token Program (SPL Token)';
        } else if (address === 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo') {
            description = 'Meteora DLMM Program';
        } else if (address === 'D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6') {
            description = 'Event Authority';
        } else if (index >= 14) {
            // BinArray PDA (динамически добавленные) - начинаются с 14 в CLAIMFEE2
            const binArrayNumber = index - 13;
            const boundaryName = binArrayNumber === 1 ? 'НИЖНЯЯ ГРАНИЦА' : 'ВЕРХНЯЯ ГРАНИЦА';
            description = `BinArray PDA #${binArrayNumber} (${boundaryName})`;
        }

        // Добавляем флаги
        const key = instruction.keys[index];
        const flags = [];
        if (key.isSigner) flags.push('signer');
        if (key.isWritable) flags.push('writable');

        const flagsStr = flags.length > 0 ? ` (${flags.join(' ')})` : '';

        return `${description}${flagsStr}`;
    }

    /**
     * 🧪 ТЕСТИРОВАНИЕ РУЧНОЙ ИНСТРУКЦИИ БЕЗ SDK
     */
    async testManualInstruction() {
        console.log(`🧪 ТЕСТИРОВАНИЕ СОЗДАНИЯ РУЧНОЙ ADD LIQUIDITY ИНСТРУКЦИИ БЕЗ SDK...`);

        try {
            // Тестовые данные
            const poolAddress = new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6');
            const positionPubKey = new PublicKey('Gbv33r6KGduHGWb8D5m4jhJMTqnGE4oaMun7ToHLr1UC');
            const userPublicKey = new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV');

            // Получение ATA аккаунтов
            console.log(`   🔍 Создание ATA для WSOL...`);
            const userTokenX = await getAssociatedTokenAddress(
                new PublicKey('So********************************111111112'), // WSOL
                userPublicKey
            );
            console.log(`   ✅ userTokenX: ${userTokenX.toString()}`);

            console.log(`   🔍 Создание ATA для USDC...`);
            console.log(`   🔍 USDC mint: EPjFWdd5IvbdBxXmuCiod1w1fzYFdVa7ygB8YtaDW7hk`);
            console.log(`   🔍 User: ${userPublicKey.toString()}`);

            let userTokenY;
            try {
                // Правильный USDC mint адрес (исправлен)
                const usdcMint = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');
                console.log(`   ✅ USDC mint создан успешно: ${usdcMint.toString()}`);

                userTokenY = await getAssociatedTokenAddress(usdcMint, userPublicKey);
                console.log(`   ✅ userTokenY: ${userTokenY.toString()}`);
            } catch (error) {
                console.log(`   ❌ Ошибка создания USDC ATA: ${error.message}`);
                console.log(`   🔍 Используем фиксированный USDC ATA адрес`);
                userTokenY = new PublicKey('6A5NHCj1yF6urc9wZNe6Bcjj4LVszQNj5DwAWG97yzMu'); // Фиксированный USDC ATA
            }

            // Параметры ликвидности
            const totalXAmount = new BN(5000000000); // 5 SOL
            const totalYAmount = new BN(4390000000000); // 4390 USDC
            const activeBinId = -4488; // Тестовый активный бин
            const minBinId = activeBinId - 1;
            const maxBinId = activeBinId + 1;

            console.log(`   📊 Тестовые параметры:`);
            console.log(`      Pool: ${poolAddress.toString()}`);
            console.log(`      Position: ${positionPubKey.toString()}`);
            console.log(`      User: ${userPublicKey.toString()}`);
            console.log(`      Active Bin: ${activeBinId}`);
            console.log(`      Range: ${minBinId} - ${maxBinId}`);

            // 🔥 ГЕНЕРИРУЕМ РЕАЛЬНЫЕ BINARRAY PDA
            console.log(`   🔥 ГЕНЕРАЦИЯ РЕАЛЬНЫХ BINARRAY PDA...`);
            const binArrayPDAs = await this.generateBinArrayPDAs(poolAddress, activeBinId, minBinId, maxBinId);

            // Создание remaining accounts info с реальными BinArray
            const remainingAccountsInfo = this.createRemainingAccountsInfo(binArrayPDAs);
            console.log(`   ✅ Создано ${binArrayPDAs.length} BinArray PDA для remaining accounts`);

            // 🔥 СОЗДАНИЕ DLMM ОБЪЕКТА С РЕАЛЬНЫМИ АДРЕСАМИ ИЗ ОСНОВНОГО КОДА!
            const poolReserves = this.getPoolReservesFromCache(poolAddress.toString());
            const dlmm = {
                pubkey: poolAddress,
                lbPair: {
                    binArrayBitmapExtension: await this.getBinArrayBitmapExtension(poolAddress), // РЕАЛЬНЫЙ PDA!
                    reserveX: poolReserves.reserveX, // РЕАЛЬНЫЙ Reserve X из кэша
                    reserveY: poolReserves.reserveY, // РЕАЛЬНЫЙ Reserve Y из кэша
                    tokenXMint: new PublicKey('So********************************111111112'), // WSOL
                    tokenYMint: new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'), // USDC
                    eventAuthority: new PublicKey('D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6') // РЕАЛЬНЫЙ Event Authority
                }
            };

            console.log(`   ✅ РЕАЛЬНЫЕ АДРЕСА ЗАГРУЖЕНЫ:`);
            console.log(`      Reserve X: ${poolReserves.reserveX.toString().slice(0,8)}...`);
            console.log(`      Reserve Y: ${poolReserves.reserveY.toString().slice(0,8)}...`);
            console.log(`      Bitmap Extension: ${dlmm.lbPair.binArrayBitmapExtension.toString().slice(0,8)}...`);

            // Создание ручной инструкции
            console.log(`   🔍 Вызов createManualAddLiquidityByStrategy2...`);
            let instruction;
            try {
                instruction = await this.createManualAddLiquidityByStrategy2({
                    positionPubKey,
                    user: userPublicKey,
                    totalXAmount,
                    totalYAmount,
                    activeBinId,
                    minBinId,
                    maxBinId,
                    remainingAccountsInfo,
                    dlmm,
                    userTokenX,
                    userTokenY
                });
                console.log(`   ✅ createManualAddLiquidityByStrategy2 выполнен успешно`);
            } catch (manualError) {
                console.log(`   ❌ Ошибка в createManualAddLiquidityByStrategy2: ${manualError.message}`);
                throw manualError;
            }

            console.log(`✅ РУЧНАЯ ИНСТРУКЦИЯ СОЗДАНА УСПЕШНО!`);
            console.log(`   📊 Program ID: ${instruction.programId.toString()}`);
            console.log(`   📊 Data length: ${instruction.data.length} bytes`);
            console.log(`   📊 Keys count: ${instruction.keys.length}`);

            // Анализ ключей с полным описанием
            console.log(`   🔑 СТРУКТУРА АККАУНТОВ:`);
            instruction.keys.forEach((key, keyIndex) => {
                const shortAddr = key.pubkey.toString().slice(0,8) + '...';
                const description = this.getAccountDescription(key.pubkey, keyIndex, instruction);

                console.log(`      [${keyIndex}] ${shortAddr} - ${description}`);
            });

            // 🔥 РАСЧЕТ ВЕСА ИНСТРУКЦИИ С ALT СЖАТИЕМ
            const instructionWeight = await this.calculateInstructionWeight(instruction);
            console.log(`   ⚖️ ФИНАЛЬНЫЙ ВЕС ИНСТРУКЦИИ:`);
            console.log(`      📦 Без сжатия: ${instructionWeight.uncompressedBytes} байт`);
            console.log(`      🗜️ С ALT сжатием: ${instructionWeight.compressedBytes} байт`);
            console.log(`      💾 Экономия: ${instructionWeight.savings} байт`);
            console.log(`      ⚡ Compute Units: ${instructionWeight.computeUnits} CU`);

            return instruction;
        } catch (error) {
            console.log(`   ❌ Ошибка в testManualInstruction: ${error.message}`);
            throw error;
        }
    }

    /**
     * 🔥 ТЕСТ CLAIMFEE2 ИНСТРУКЦИИ (ОТКЛЮЧЕННОЙ В ОСНОВНОМ КОДЕ)
     */
    async testClaimFee2Instruction() {
        console.log(`🔥 ТЕСТИРОВАНИЕ CLAIMFEE2 ИНСТРУКЦИИ (ОТКЛЮЧЕННОЙ В ОСНОВНОМ КОДЕ)...`);

        try {
            // Тестовые параметры
            const poolAddress = new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6');
            const activeBinId = -4488;
            const minBinId = activeBinId - 1;
            const maxBinId = activeBinId + 1;

            console.log(`   📊 Тестовые параметры:`);
            console.log(`      Pool: ${poolAddress.toString().slice(0,8)}...`);
            console.log(`      Active Bin: ${activeBinId}`);
            console.log(`      Range: ${minBinId} - ${maxBinId}`);

            // Создание CLAIMFEE2 инструкции
            console.log(`   🔍 Вызов createClaimFee2Instruction...`);
            const claimFeeInstruction = await this.createClaimFee2Instruction(poolAddress, activeBinId, minBinId, maxBinId);
            console.log(`   ✅ createClaimFee2Instruction выполнен успешно`);

            console.log(`✅ CLAIMFEE2 ИНСТРУКЦИЯ СОЗДАНА УСПЕШНО!`);
            console.log(`   📊 Program ID: ${claimFeeInstruction.programId.toString()}`);
            console.log(`   📊 Data length: ${claimFeeInstruction.data.length} bytes`);
            console.log(`   📊 Keys count: ${claimFeeInstruction.keys.length}`);

            // Анализ ключей с полным описанием
            console.log(`   🔑 СТРУКТУРА АККАУНТОВ CLAIMFEE2:`);
            claimFeeInstruction.keys.forEach((key, keyIndex) => {
                const shortAddr = key.pubkey.toString().slice(0,8) + '...';
                const description = this.getClaimFeeAccountDescription(key.pubkey, keyIndex, claimFeeInstruction);

                console.log(`      [${keyIndex}] ${shortAddr} - ${description}`);
            });

            // 🔥 РАСЧЕТ ВЕСА CLAIMFEE2 ИНСТРУКЦИИ
            const claimFeeWeight = await this.calculateInstructionWeight(claimFeeInstruction);
            console.log(`   ⚖️ ФИНАЛЬНЫЙ ВЕС CLAIMFEE2 ИНСТРУКЦИИ:`);
            console.log(`      📦 Без сжатия: ${claimFeeWeight.uncompressedBytes} байт`);
            console.log(`      🗜️ С ALT сжатием: ${claimFeeWeight.compressedBytes} байт`);
            console.log(`      💾 Экономия: ${claimFeeWeight.savings} байт`);
            console.log(`      ⚡ Compute Units: ${claimFeeWeight.computeUnits} CU`);

            return claimFeeInstruction;

        } catch (error) {
            console.log(`❌ ТЕСТ ПРОВАЛЕН: ${error.message}`);
            throw error;
        }
    }
}

// Экспорт для использования в других файлах
module.exports = AddLiquidityDebugger;

// Запуск теста если файл запущен напрямую
if (require.main === module) {
    const liquidityDebugger = new AddLiquidityDebugger();

    async function runAllTests() {
        try {
            // Тест ADD LIQUIDITY
            console.log(`🔥 ТЕСТИРОВАНИЕ ADD LIQUIDITY ИНСТРУКЦИИ...`);
            await liquidityDebugger.testManualInstruction();
            console.log(`🔥 ADD LIQUIDITY ТЕСТ ЗАВЕРШЕН!\n`);

            // Тест CLAIMFEE2
            console.log(`🔥 ТЕСТИРОВАНИЕ CLAIMFEE2 ИНСТРУКЦИИ...`);
            await liquidityDebugger.testClaimFee2Instruction();
            console.log(`🔥 CLAIMFEE2 ТЕСТ ЗАВЕРШЕН!\n`);

            console.log(`🎯 ВСЯ ОТЛАДКА ЗАВЕРШЕНА!`);
            process.exit(0);
        } catch (error) {
            console.log(`💥 КРИТИЧЕСКАЯ ОШИБКА: ${error.message}`);
            process.exit(1);
        }
    }

    runAllTests();
}
