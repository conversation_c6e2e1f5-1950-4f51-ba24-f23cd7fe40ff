/**
 * 🔥 ИЗМЕРЕНИЕ ВЕСА ВСЕХ ИНСТРУКЦИЙ FLASH LOAN АРБИТРАЖА
 */

require('dotenv').config();

const { Connection, Keypair, PublicKey, TransactionInstruction } = require('@solana/web3.js');
const { TOKEN_PROGRAM_ID, getAssociatedTokenAddress, createAssociatedTokenAccountIdempotentInstruction } = require('@solana/spl-token');
const { BN } = require('@coral-xyz/anchor');

class FlashLoanInstructionMeasurer {
    constructor() {
        // 🔥 ПРОГРАММЫ
        this.MARGINFI_PROGRAM = new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA');
        this.METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        this.TOKEN_PROGRAM = TOKEN_PROGRAM_ID;
        this.SYSTEM_PROGRAM = new PublicKey('********************************');
        
        // 🔥 РЕАЛЬНЫЕ АДРЕСА ИЗ ОСНОВНОГО КОДА (ТОЛЬКО PRODUCTION!)
        this.userWallet = new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV');
        this.marginfiAccount = new PublicKey('4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8'); // РЕАЛЬНЫЙ MarginFi Account

        // ПУЛЫ (РЕАЛЬНЫЕ ИЗ trading-config.js)
        this.POOL_1_ADDRESS = new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6');
        this.POOL_2_ADDRESS = new PublicKey('BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y'); // РЕАЛЬНЫЙ Pool 2

        // RESERVES (РЕАЛЬНЫЕ ИЗ complete-flash-loan-structure.js)
        this.POOL_1_RESERVE_X = new PublicKey('EYj9xKw6ZszwpyNibHY7JD5o3QgTVrSdcBp1fMJhrR9o');
        this.POOL_1_RESERVE_Y = new PublicKey('CoaxzEh8p5YyGLcj36Eo3cUThVJxeKCs7qvLAGDYwBcz');
        this.POOL_2_RESERVE_X = new PublicKey('DwZz4S1Z1LBXomzmncQRVKCYhjCqSAMQ6RPKbUAadr7H'); // РЕАЛЬНЫЙ Pool 2 Reserve X
        this.POOL_2_RESERVE_Y = new PublicKey('4N22J4vW2juHocTntJNmXywSonYjkndCwahjZ2cYLDgb'); // РЕАЛЬНЫЙ Pool 2 Reserve Y

        // POSITIONS (РЕАЛЬНЫЕ ИЗ trading-config.js)
        this.POOL_1_POSITION = new PublicKey('Gbv33r6KGduHGWb8D5m4jhJMTqnGE4oaMun7ToHLr1UC');
        this.POOL_2_POSITION = new PublicKey('Axf1TsquSoML5qJ7QyM1jxLPbHRazc9r4xBYjcADnz3S'); // РЕАЛЬНЫЙ Pool 2 Position
        
        // MINTS
        this.WSOL_MINT = new PublicKey('So********************************111111112');
        this.USDC_MINT = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');
        
        // BANKS (РЕАЛЬНЫЕ ИЗ complete-flash-loan-structure.js)
        this.USDC_BANK = new PublicKey('2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB'); // РЕАЛЬНЫЙ USDC Bank
        this.SOL_BANK = new PublicKey('CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh');  // РЕАЛЬНЫЙ SOL Bank

        // VAULTS (РЕАЛЬНЫЕ ИЗ complete-flash-loan-structure.js)
        this.USDC_VAULT = new PublicKey('HmpMfL8942u22htC4EMiWgLX931g3sacXFR6KjuLgKLV'); // USDC Liquidity Vault
        this.SOL_VAULT = new PublicKey('CapuXNQoDviLvU1PxFiizLgPNQCxrsag1uMeyk6zLVps');  // SOL Liquidity Vault

        // VAULT AUTHORITIES (РЕАЛЬНЫЕ)
        this.USDC_VAULT_AUTHORITY = new PublicKey('D8cy77BBepLMngZx6ZukaTff5hCt1HrWyKk3Hnd9oitf');
        this.SOL_VAULT_AUTHORITY = new PublicKey('Bohoc1ikHLD7xKJuzTyiTyCwzaL5N7ggJQu75A8mKYM8');

        // SYSVARS (РЕАЛЬНЫЕ)
        this.INSTRUCTIONS_SYSVAR = new PublicKey('Sysvar1nstructions1111111111111111111111111');
        this.RENT_SYSVAR = new PublicKey('SysvarRent********************************1');

        // METEORA SPECIFIC (РЕАЛЬНЫЕ)
        this.EVENT_AUTHORITY = new PublicKey('D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6');
        this.MEMO_PROGRAM = new PublicKey('MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr');
        
        // ALT таблицы
        this.altMap = new Map();
        this.loadALTTables();
    }

    /**
     * 🔥 ЗАГРУЗКА ALT ТАБЛИЦ
     */
    loadALTTables() {
        try {
            const fs = require('fs');
            const altData = JSON.parse(fs.readFileSync('custom-alt-data.json', 'utf8'));
            
            // MarginFi ALT
            if (altData.tables.marginfi1) {
                altData.tables.marginfi1.addresses.forEach((address, index) => {
                    this.altMap.set(address, { tableIndex: 0, addressIndex: index, tableName: 'MarginFi' });
                });
            }

            // Custom ALT
            if (altData.tables.custom) {
                altData.tables.custom.addresses.forEach((address, index) => {
                    this.altMap.set(address, { tableIndex: 1, addressIndex: index, tableName: 'Custom' });
                });
            }

            console.log(`✅ ALT таблицы загружены: ${this.altMap.size} адресов`);
        } catch (error) {
            console.log(`⚠️ ALT таблицы не загружены: ${error.message}`);
        }
    }

    /**
     * 🔥 ПРАВИЛЬНЫЙ РАСЧЕТ ВЕСА ИНСТРУКЦИИ С ALT СЖАТИЕМ
     */
    calculateInstructionWeight(instruction, name) {
        console.log(`\n🔍 ДЕТАЛЬНЫЙ РАСЧЕТ ДЛЯ ${name}:`);

        // 🔥 ВЕС БЕЗ СЖАТИЯ
        const programIdBytes = 1; // Program ID в транзакции = 1 байт (индекс)
        const dataBytes = instruction.data.length;
        const accountBytes = instruction.keys.length * 1; // Каждый аккаунт = 1 байт (индекс в accounts array)
        const instructionMetadata = 1; // Метаданные самой инструкции

        // Аккаунты в accounts array транзакции (32 байта каждый)
        const accountsArrayBytes = instruction.keys.length * 32;

        const uncompressedBytes = programIdBytes + dataBytes + accountBytes + instructionMetadata + accountsArrayBytes;

        console.log(`   📊 БЕЗ СЖАТИЯ:`);
        console.log(`      Program ID: ${programIdBytes} байт`);
        console.log(`      Данные: ${dataBytes} байт`);
        console.log(`      Аккаунты индексы: ${accountBytes} байт`);
        console.log(`      Метаданные: ${instructionMetadata} байт`);
        console.log(`      Accounts array: ${accountsArrayBytes} байт`);
        console.log(`      ИТОГО: ${uncompressedBytes} байт`);

        // 🔥 ALT СЖАТИЕ
        let compressibleCount = 0;
        let nonCompressibleCount = 0;

        instruction.keys.forEach(key => {
            const addressStr = key.pubkey.toString();
            if (this.altMap.has(addressStr) && !key.isSigner) {
                compressibleCount++;
            } else {
                nonCompressibleCount++;
            }
        });

        // 🔥 ПРАВИЛЬНЫЙ РАСЧЕТ ALT СЖАТИЯ (БЕЗ OVERHEAD НА ИНСТРУКЦИЮ!)
        const altSavings = compressibleCount * 32; // Экономия от сжатых аккаунтов

        let compressedBytes;
        let actualSavings;

        if (compressibleCount > 0) {
            // ALT сжатие применяется - убираем сжимаемые аккаунты из accounts array
            const compressedAccountsArrayBytes = nonCompressibleCount * 32;
            compressedBytes = programIdBytes + dataBytes + accountBytes + instructionMetadata + compressedAccountsArrayBytes;
            actualSavings = uncompressedBytes - compressedBytes;

            console.log(`   🗜️ С ALT СЖАТИЕМ:`);
            console.log(`      Сжимаемые аккаунты: ${compressibleCount} × 32 = ${altSavings} байт → 0 байт`);
            console.log(`      Несжимаемые аккаунты: ${nonCompressibleCount} × 32 = ${compressedAccountsArrayBytes} байт`);
            console.log(`      Экономия на инструкции: ${actualSavings} байт`);
            console.log(`      ИТОГО: ${compressedBytes} байт`);
        } else {
            // Нет сжимаемых аккаунтов
            compressedBytes = uncompressedBytes;
            actualSavings = 0;

            console.log(`   🗜️ БЕЗ ALT СЖАТИЯ:`);
            console.log(`      Сжимаемые аккаунты: 0`);
            console.log(`      ИТОГО: ${compressedBytes} байт (без изменений)`);
        }

        // Compute Units (не изменяются от сжатия)
        const baseWeight = this.getBaseWeight(name);
        const accountWeight = instruction.keys.length * 100;
        const writableWeight = instruction.keys.filter(k => k.isWritable).length * 200;
        const signerWeight = instruction.keys.filter(k => k.isSigner).length * 300;
        const dataWeight = instruction.data.length * 10;
        const computeUnits = baseWeight + accountWeight + writableWeight + signerWeight + dataWeight;

        return {
            name,
            accounts: instruction.keys.length,
            dataBytes,
            uncompressedBytes,
            compressedBytes,
            savings: actualSavings,
            computeUnits,
            compressibleAccounts: compressibleCount,
            nonCompressibleAccounts: nonCompressibleCount,
            altUsed: compressibleCount > 0
        };
    }

    /**
     * 🔥 БАЗОВЫЙ ВЕС ПО ТИПУ ИНСТРУКЦИИ
     */
    getBaseWeight(name) {
        const weights = {
            'START Flash Loan': 5000,
            'END Flash Loan': 5000,
            'CREATE ATA': 3000,
            'BORROW': 8000,
            'REPAY': 8000,
            'ADD Liquidity': 15000,
            'REMOVE Liquidity': 12000,
            'SWAP': 10000,
            'CLAIMFEE2': 8000
        };

        for (const [key, weight] of Object.entries(weights)) {
            if (name.includes(key)) return weight;
        }
        return 5000; // Дефолтный
    }

    /**
     * 🔥 1. START FLASH LOAN
     */
    createStartFlashLoanInstruction() {
        const discriminator = Buffer.from([14, 131, 33, 220, 81, 186, 180, 107]);
        const endIndex = Buffer.alloc(8);
        endIndex.writeBigUInt64LE(BigInt(13), 0); // 13 инструкций до END
        
        const data = Buffer.concat([discriminator, endIndex]);
        const INSTRUCTIONS_SYSVAR = new PublicKey('Sysvar1nstructions1111111111111111111111111');

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: [
                { pubkey: this.marginfiAccount, isSigner: false, isWritable: true },
                { pubkey: this.userWallet, isSigner: true, isWritable: true },
                { pubkey: this.INSTRUCTIONS_SYSVAR, isSigner: false, isWritable: false }
            ],
            data
        });
    }

    /**
     * 🔥 2. CREATE USDC ATA
     */
    async createUSDCATAInstruction() {
        const usdcATA = await getAssociatedTokenAddress(this.USDC_MINT, this.userWallet);
        
        return createAssociatedTokenAccountIdempotentInstruction(
            this.userWallet,  // payer
            usdcATA,          // ata
            this.userWallet,  // owner
            this.USDC_MINT    // mint
        );
    }

    /**
     * 🔥 3. CREATE SOL ATA
     */
    async createSOLATAInstruction() {
        const solATA = await getAssociatedTokenAddress(this.WSOL_MINT, this.userWallet);
        
        return createAssociatedTokenAccountIdempotentInstruction(
            this.userWallet,  // payer
            solATA,           // ata
            this.userWallet,  // owner
            this.WSOL_MINT    // mint
        );
    }

    /**
     * 🔥 4. BORROW USDC
     */
    async createBorrowUSDCInstruction() {
        const discriminator = Buffer.from([228, 253, 131, 202, 207, 116, 89, 18]);
        const amount = Buffer.alloc(8);
        amount.writeBigUInt64LE(BigInt(**********), 0); // 1000 USDC
        
        const data = Buffer.concat([discriminator, amount]);

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: [
                { pubkey: this.marginfiAccount, isSigner: false, isWritable: true },
                { pubkey: this.userWallet, isSigner: true, isWritable: false },
                { pubkey: this.USDC_BANK, isSigner: false, isWritable: true },
                { pubkey: await getAssociatedTokenAddress(this.USDC_MINT, this.userWallet), isSigner: false, isWritable: true }, // destination USDC ATA
                { pubkey: this.USDC_VAULT_AUTHORITY, isSigner: false, isWritable: false }, // bank_liquidity_vault_authority
                { pubkey: this.USDC_VAULT, isSigner: false, isWritable: true }, // bank_liquidity_vault
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false }
            ],
            data
        });
    }

    /**
     * 🔥 5. BORROW WSOL
     */
    async createBorrowWSOLInstruction() {
        const discriminator = Buffer.from([228, 253, 131, 202, 207, 116, 89, 18]);
        const amount = Buffer.alloc(8);
        amount.writeBigUInt64LE(BigInt(**********), 0); // 5 WSOL

        const data = Buffer.concat([discriminator, amount]);

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: [
                { pubkey: this.marginfiAccount, isSigner: false, isWritable: true },
                { pubkey: this.userWallet, isSigner: true, isWritable: false },
                { pubkey: this.SOL_BANK, isSigner: false, isWritable: true },
                { pubkey: await getAssociatedTokenAddress(this.WSOL_MINT, this.userWallet), isSigner: false, isWritable: true }, // destination WSOL ATA
                { pubkey: this.SOL_VAULT_AUTHORITY, isSigner: false, isWritable: false }, // bank_liquidity_vault_authority
                { pubkey: this.SOL_VAULT, isSigner: false, isWritable: true }, // bank_liquidity_vault
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false }
            ],
            data
        });
    }

    /**
     * 🔥 6-7. ADD LIQUIDITY (УПРОЩЕННАЯ ВЕРСИЯ)
     */
    async createAddLiquidityInstruction(poolAddress, positionAddress) {
        const discriminator = Buffer.from([0xe4, 0xa2, 0x4e, 0x1c, 0x46, 0xdb, 0x74, 0x73]);

        // Упрощенные данные (реальные данные сложнее)
        const liquidityData = Buffer.alloc(128);
        liquidityData.writeBigUInt64LE(BigInt(**********), 0); // amount_x
        liquidityData.writeBigUInt64LE(BigInt(**********), 8); // amount_y

        const data = Buffer.concat([discriminator, liquidityData]);

        const userTokenX = await getAssociatedTokenAddress(this.WSOL_MINT, this.userWallet);
        const userTokenY = await getAssociatedTokenAddress(this.USDC_MINT, this.userWallet);

        // Определяем reserves в зависимости от пула
        const isPool1 = poolAddress.equals(this.POOL_1_ADDRESS);
        const reserveX = isPool1 ? this.POOL_1_RESERVE_X : this.POOL_2_RESERVE_X;
        const reserveY = isPool1 ? this.POOL_1_RESERVE_Y : this.POOL_2_RESERVE_Y;

        return new TransactionInstruction({
            programId: this.METEORA_DLMM_PROGRAM,
            keys: [
                { pubkey: positionAddress, isSigner: false, isWritable: true },
                { pubkey: poolAddress, isSigner: false, isWritable: true },
                { pubkey: this.EVENT_AUTHORITY, isSigner: false, isWritable: false }, // bitmap extension (используем event authority)
                { pubkey: userTokenX, isSigner: false, isWritable: true },
                { pubkey: userTokenY, isSigner: false, isWritable: true },
                { pubkey: reserveX, isSigner: false, isWritable: true },
                { pubkey: reserveY, isSigner: false, isWritable: true },
                { pubkey: this.WSOL_MINT, isSigner: false, isWritable: false },
                { pubkey: this.USDC_MINT, isSigner: false, isWritable: false },
                { pubkey: this.userWallet, isSigner: true, isWritable: true },
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
                { pubkey: this.EVENT_AUTHORITY, isSigner: false, isWritable: false },
                { pubkey: this.METEORA_DLMM_PROGRAM, isSigner: false, isWritable: false },
                { pubkey: this.WSOL_MINT, isSigner: false, isWritable: true }, // binarray 1 (используем реальные mints)
                { pubkey: this.USDC_MINT, isSigner: false, isWritable: true }  // binarray 2
            ],
            data
        });
    }

    /**
     * 🔥 8-9. SWAP INSTRUCTIONS
     */
    async createSwapInstruction(direction) {
        const discriminator = Buffer.from([0xf8, 0xc6, 0x9e, 0x91, 0xe1, 0x75, 0x87, 0xc8]); // swap discriminator

        const swapData = Buffer.alloc(32);
        swapData.writeBigUInt64LE(BigInt(**********), 0); // amount
        swapData.writeUInt8(direction === 'BUY' ? 1 : 0, 8); // direction

        const data = Buffer.concat([discriminator, swapData]);

        const userTokenX = await getAssociatedTokenAddress(this.WSOL_MINT, this.userWallet);
        const userTokenY = await getAssociatedTokenAddress(this.USDC_MINT, this.userWallet);

        return new TransactionInstruction({
            programId: this.METEORA_DLMM_PROGRAM,
            keys: [
                { pubkey: this.POOL_1_ADDRESS, isSigner: false, isWritable: true },
                { pubkey: userTokenX, isSigner: false, isWritable: true },
                { pubkey: userTokenY, isSigner: false, isWritable: true },
                { pubkey: this.POOL_1_RESERVE_X, isSigner: false, isWritable: true },
                { pubkey: this.POOL_1_RESERVE_Y, isSigner: false, isWritable: true },
                { pubkey: this.userWallet, isSigner: true, isWritable: false },
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
                { pubkey: this.SYSTEM_PROGRAM, isSigner: false, isWritable: true }, // binarray
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false }  // oracle
            ],
            data
        });
    }

    /**
     * 🔥 10-11. REMOVE LIQUIDITY
     */
    async createRemoveLiquidityInstruction(poolAddress, positionAddress) {
        const discriminator = Buffer.from([0x80, 0x8d, 0x21, 0x4f, 0x5e, 0x7e, 0x58, 0x48]); // remove_liquidity discriminator

        const removeData = Buffer.alloc(64);
        removeData.writeInt32LE(-4489, 0); // min_bin_id
        removeData.writeInt32LE(-4487, 4); // max_bin_id
        removeData.writeBigUInt64LE(BigInt(**********), 8); // bps_to_remove

        const data = Buffer.concat([discriminator, removeData]);

        const userTokenX = await getAssociatedTokenAddress(this.WSOL_MINT, this.userWallet);
        const userTokenY = await getAssociatedTokenAddress(this.USDC_MINT, this.userWallet);

        return new TransactionInstruction({
            programId: this.METEORA_DLMM_PROGRAM,
            keys: [
                { pubkey: positionAddress, isSigner: false, isWritable: true },
                { pubkey: poolAddress, isSigner: false, isWritable: true },
                { pubkey: this.SYSTEM_PROGRAM, isSigner: false, isWritable: false }, // bitmap extension
                { pubkey: userTokenX, isSigner: false, isWritable: true },
                { pubkey: userTokenY, isSigner: false, isWritable: true },
                { pubkey: this.POOL_1_RESERVE_X, isSigner: false, isWritable: true },
                { pubkey: this.POOL_1_RESERVE_Y, isSigner: false, isWritable: true },
                { pubkey: this.userWallet, isSigner: true, isWritable: false },
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
                { pubkey: this.METEORA_DLMM_PROGRAM, isSigner: false, isWritable: false },
                { pubkey: this.WSOL_MINT, isSigner: false, isWritable: true }, // binarray 1
                { pubkey: this.USDC_MINT, isSigner: false, isWritable: true }  // binarray 2
            ],
            data
        });
    }

    /**
     * 🔥 12. CLAIMFEE2
     */
    async createClaimFee2Instruction() {
        const discriminator = Buffer.from([0x70, 0xbf, 0x65, 0xab, 0x1c, 0x90, 0x7f, 0xbb]);

        const claimData = Buffer.alloc(8);
        claimData.writeInt32LE(-4489, 0); // min_bin_id
        claimData.writeInt32LE(-4487, 4); // max_bin_id

        const data = Buffer.concat([discriminator, claimData]);

        const userTokenX = await getAssociatedTokenAddress(this.WSOL_MINT, this.userWallet);
        const userTokenY = await getAssociatedTokenAddress(this.USDC_MINT, this.userWallet);

        return new TransactionInstruction({
            programId: this.METEORA_DLMM_PROGRAM,
            keys: [
                { pubkey: this.POOL_1_ADDRESS, isSigner: false, isWritable: true },
                { pubkey: this.POOL_1_POSITION, isSigner: false, isWritable: true },
                { pubkey: this.userWallet, isSigner: true, isWritable: true },
                { pubkey: this.POOL_1_RESERVE_X, isSigner: false, isWritable: true },
                { pubkey: this.POOL_1_RESERVE_Y, isSigner: false, isWritable: true },
                { pubkey: userTokenX, isSigner: false, isWritable: true },
                { pubkey: userTokenY, isSigner: false, isWritable: true },
                { pubkey: this.WSOL_MINT, isSigner: false, isWritable: false },
                { pubkey: this.USDC_MINT, isSigner: false, isWritable: false },
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
                { pubkey: new PublicKey('MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr'), isSigner: false, isWritable: false },
                { pubkey: new PublicKey('D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6'), isSigner: false, isWritable: false },
                { pubkey: this.METEORA_DLMM_PROGRAM, isSigner: false, isWritable: false },
                { pubkey: this.WSOL_MINT, isSigner: false, isWritable: true }, // binarray 1
                { pubkey: this.USDC_MINT, isSigner: false, isWritable: true }  // binarray 2
            ],
            data
        });
    }

    /**
     * 🔥 13-14. REPAY INSTRUCTIONS
     */
    async createRepayUSDCInstruction() {
        const discriminator = Buffer.from([234, 103, 67, 82, 208, 234, 219, 166]);
        const amount = Buffer.alloc(8);
        amount.writeBigUInt64LE(BigInt(**********), 0); // 1000 USDC

        const data = Buffer.concat([discriminator, amount]);

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: [
                { pubkey: this.marginfiAccount, isSigner: false, isWritable: true },
                { pubkey: this.userWallet, isSigner: true, isWritable: false },
                { pubkey: this.USDC_BANK, isSigner: false, isWritable: true },
                { pubkey: this.SYSTEM_PROGRAM, isSigner: false, isWritable: false }, // source_token_account
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: true }, // bank_liquidity_vault
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false }
            ],
            data
        });
    }

    async createRepayWSOLInstruction() {
        const discriminator = Buffer.from([234, 103, 67, 82, 208, 234, 219, 166]);
        const amount = Buffer.alloc(8);
        amount.writeBigUInt64LE(BigInt(**********), 0); // 5 WSOL

        const data = Buffer.concat([discriminator, amount]);

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: [
                { pubkey: this.marginfiAccount, isSigner: false, isWritable: true },
                { pubkey: this.userWallet, isSigner: true, isWritable: false },
                { pubkey: this.SOL_BANK, isSigner: false, isWritable: true },
                { pubkey: this.SYSTEM_PROGRAM, isSigner: false, isWritable: false },
                { pubkey: this.SOL_VAULT, isSigner: false, isWritable: true },
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false }
            ],
            data
        });
    }

    /**
     * 🔥 15. END FLASH LOAN
     */
    createEndFlashLoanInstruction() {
        const discriminator = Buffer.from([163, 52, 200, 231, 140, 3, 69, 186]);

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: [
                { pubkey: this.marginfiAccount, isSigner: false, isWritable: true },
                { pubkey: this.userWallet, isSigner: true, isWritable: false }
            ],
            data: discriminator
        });
    }

    /**
     * 🔥 ОСНОВНОЙ МЕТОД ИЗМЕРЕНИЯ
     */
    async measureAllInstructions() {
        console.log('🔥 ИЗМЕРЕНИЕ ВЕСА ВСЕХ ИНСТРУКЦИЙ FLASH LOAN АРБИТРАЖА\n');

        const instructions = [];
        
        // 1. START Flash Loan
        instructions.push({
            instruction: this.createStartFlashLoanInstruction(),
            name: 'START Flash Loan'
        });

        // 2-3. CREATE ATA
        instructions.push({
            instruction: await this.createUSDCATAInstruction(),
            name: 'CREATE USDC ATA'
        });
        
        instructions.push({
            instruction: await this.createSOLATAInstruction(),
            name: 'CREATE SOL ATA'
        });

        // 4-5. BORROW
        instructions.push({
            instruction: await this.createBorrowUSDCInstruction(),
            name: 'BORROW USDC'
        });

        instructions.push({
            instruction: await this.createBorrowWSOLInstruction(),
            name: 'BORROW WSOL'
        });

        // 6-7. ADD LIQUIDITY
        instructions.push({
            instruction: await this.createAddLiquidityInstruction(this.POOL_1_ADDRESS, this.POOL_1_POSITION),
            name: 'ADD Liquidity Pool 1'
        });

        instructions.push({
            instruction: await this.createAddLiquidityInstruction(this.POOL_2_ADDRESS, this.POOL_2_POSITION),
            name: 'ADD Liquidity Pool 2'
        });

        // 8-9. SWAP
        instructions.push({
            instruction: await this.createSwapInstruction('BUY'),
            name: 'BUY Swap (USDC → WSOL)'
        });

        instructions.push({
            instruction: await this.createSwapInstruction('SELL'),
            name: 'SELL Swap (WSOL → USDC)'
        });

        // 10-11. REMOVE LIQUIDITY
        instructions.push({
            instruction: await this.createRemoveLiquidityInstruction(this.POOL_1_ADDRESS, this.POOL_1_POSITION),
            name: 'REMOVE Liquidity Pool 1'
        });

        instructions.push({
            instruction: await this.createRemoveLiquidityInstruction(this.POOL_2_ADDRESS, this.POOL_2_POSITION),
            name: 'REMOVE Liquidity Pool 2'
        });

        // 12. CLAIMFEE2
        instructions.push({
            instruction: await this.createClaimFee2Instruction(),
            name: 'CLAIMFEE2'
        });

        // 13-14. REPAY
        instructions.push({
            instruction: await this.createRepayUSDCInstruction(),
            name: 'REPAY USDC'
        });

        instructions.push({
            instruction: await this.createRepayWSOLInstruction(),
            name: 'REPAY WSOL'
        });

        // 15. END Flash Loan
        instructions.push({
            instruction: this.createEndFlashLoanInstruction(),
            name: 'END Flash Loan'
        });

        // Измеряем все инструкции
        const results = [];
        let totalUncompressed = 0;
        let totalCompressed = 0;
        let totalComputeUnits = 0;

        console.log('📊 ДЕТАЛЬНЫЙ АНАЛИЗ КАЖДОЙ ИНСТРУКЦИИ:\n');

        instructions.forEach((item, index) => {
            const weight = this.calculateInstructionWeight(item.instruction, item.name);
            results.push(weight);
            
            totalUncompressed += weight.uncompressedBytes;
            totalCompressed += weight.compressedBytes;
            totalComputeUnits += weight.computeUnits;

            console.log(`${index + 1}. ${weight.name}:`);
            console.log(`   📦 Аккаунты: ${weight.accounts}`);
            console.log(`   📄 Данные: ${weight.dataBytes} байт`);
            console.log(`   📊 Без сжатия: ${weight.uncompressedBytes} байт`);
            console.log(`   🗜️ С ALT сжатием: ${weight.compressedBytes} байт`);
            console.log(`   💾 Экономия: ${weight.savings} байт`);
            console.log(`   ⚡ Compute Units: ${weight.computeUnits} CU`);
            console.log(`   🔗 ALT сжимаемые: ${weight.compressibleAccounts}/${weight.accounts}\n`);
        });

        // 🔥 ПРАВИЛЬНЫЙ РАСЧЕТ С ALT OVERHEAD НА ТРАНЗАКЦИЮ
        const altOverhead = 64; // 2 ALT таблицы × 32 байта (ОДИН РАЗ НА ВСЮ ТРАНЗАКЦИЮ!)
        const totalCompressedWithOverhead = totalCompressed + altOverhead;
        const actualTotalSavings = totalUncompressed - totalCompressedWithOverhead;

        // Итоговая статистика
        console.log('🎯 ИТОГОВАЯ СТАТИСТИКА:\n');
        console.log(`📊 Всего инструкций: ${instructions.length}`);
        console.log(`📦 Общий вес без сжатия: ${totalUncompressed} байт`);
        console.log(`🗜️ Общий вес инструкций с ALT сжатием: ${totalCompressed} байт`);
        console.log(`📋 ALT таблицы overhead (на транзакцию): ${altOverhead} байт`);
        console.log(`🎯 ИТОГОВЫЙ ВЕС ТРАНЗАКЦИИ: ${totalCompressedWithOverhead} байт`);
        console.log(`💾 Общая экономия: ${actualTotalSavings} байт (${Math.round(actualTotalSavings / totalUncompressed * 100)}%)`);
        console.log(`⚡ Общие Compute Units: ${totalComputeUnits} CU`);
        console.log(`🚨 Лимит транзакции: 1232 байт`);
        console.log(`📈 Использование: ${Math.round(totalCompressedWithOverhead / 1232 * 100)}% от лимита`);
        console.log(`🔥 Остается места: ${1232 - totalCompressedWithOverhead} байт\n`);

        return results;
    }

    /**
     * 🔥 АНАЛИЗ ДОПОЛНИТЕЛЬНЫХ АДРЕСОВ ДЛЯ ALT СЖАТИЯ
     */
    async analyzeAdditionalALTAddresses() {
        console.log('\n🔍 АНАЛИЗ ДОПОЛНИТЕЛЬНЫХ АДРЕСОВ ДЛЯ ALT СЖАТИЯ:\n');

        const additionalAddresses = [];

        // 1. BIN ARRAY PDA (динамические, но предсказуемые)
        console.log('1. 📦 BIN ARRAY PDA АДРЕСА:');
        const binArrays = await this.generateBinArrayPDAs();
        binArrays.forEach((binArray, index) => {
            console.log(`   BinArray ${index + 1}: ${binArray.toString()}`);
            additionalAddresses.push({ address: binArray.toString(), type: 'BinArray PDA', savings: 32 });
        });

        // 2. BITMAP EXTENSION PDA
        console.log('\n2. 🗺️ BITMAP EXTENSION PDA:');
        const pool1BitmapExt = await this.getBinArrayBitmapExtension(this.POOL_1_ADDRESS);
        const pool2BitmapExt = await this.getBinArrayBitmapExtension(this.POOL_2_ADDRESS);
        console.log(`   Pool 1 Bitmap Extension: ${pool1BitmapExt.toString()}`);
        console.log(`   Pool 2 Bitmap Extension: ${pool2BitmapExt.toString()}`);
        additionalAddresses.push({ address: pool1BitmapExt.toString(), type: 'Pool 1 Bitmap Extension', savings: 32 });
        additionalAddresses.push({ address: pool2BitmapExt.toString(), type: 'Pool 2 Bitmap Extension', savings: 32 });

        // 3. ORACLE АДРЕСА
        console.log('\n3. 🔮 ORACLE АДРЕСА:');
        const pool1Oracle = new PublicKey('59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li');
        const pool2Oracle = new PublicKey('ETc6tqgLrr7wXsH8u2QBK1CyXHX3kvV6WQjBz4cf3sCj');
        console.log(`   Pool 1 Oracle: ${pool1Oracle.toString()}`);
        console.log(`   Pool 2 Oracle: ${pool2Oracle.toString()}`);
        additionalAddresses.push({ address: pool1Oracle.toString(), type: 'Pool 1 Oracle', savings: 32 });
        additionalAddresses.push({ address: pool2Oracle.toString(), type: 'Pool 2 Oracle', savings: 32 });

        // 4. ДОПОЛНИТЕЛЬНЫЕ ПОЗИЦИИ (если есть)
        console.log('\n4. 🎯 ДОПОЛНИТЕЛЬНЫЕ ПОЗИЦИИ:');
        console.log(`   Pool 1 Position: ${this.POOL_1_POSITION.toString()} (уже в ALT)`);
        console.log(`   Pool 2 Position: ${this.POOL_2_POSITION.toString()}`);
        additionalAddresses.push({ address: this.POOL_2_POSITION.toString(), type: 'Pool 2 Position', savings: 32 });

        // 5. ДОПОЛНИТЕЛЬНЫЕ RESERVES
        console.log('\n5. 🏦 ДОПОЛНИТЕЛЬНЫЕ RESERVES:');
        console.log(`   Pool 2 Reserve X: ${this.POOL_2_RESERVE_X.toString()}`);
        console.log(`   Pool 2 Reserve Y: ${this.POOL_2_RESERVE_Y.toString()}`);
        additionalAddresses.push({ address: this.POOL_2_RESERVE_X.toString(), type: 'Pool 2 Reserve X', savings: 32 });
        additionalAddresses.push({ address: this.POOL_2_RESERVE_Y.toString(), type: 'Pool 2 Reserve Y', savings: 32 });

        // ИТОГОВЫЙ АНАЛИЗ
        const totalPotentialSavings = additionalAddresses.reduce((sum, addr) => sum + addr.savings, 0);
        console.log(`\n📊 ИТОГОВЫЙ АНАЛИЗ ДОПОЛНИТЕЛЬНОГО СЖАТИЯ:`);
        console.log(`   Дополнительных адресов: ${additionalAddresses.length}`);
        console.log(`   Потенциальная экономия: ${totalPotentialSavings} байт`);
        console.log(`   Текущее превышение: 237 байт`);
        console.log(`   После добавления в ALT: ${237 - totalPotentialSavings} байт`);

        if (totalPotentialSavings >= 237) {
            console.log(`   ✅ ПРОБЛЕМА РЕШЕНА! Можно добавить даже 2 CLAIMFEE2!`);
            const extraSpace = totalPotentialSavings - 237;
            const additionalClaimFee2 = Math.floor(extraSpace / 174); // CLAIMFEE2 весит 174 байта
            console.log(`   🎉 Дополнительно поместится: ${additionalClaimFee2} CLAIMFEE2 инструкций`);
        } else {
            console.log(`   ⚠️ Нужно еще ${237 - totalPotentialSavings} байт экономии`);
        }

        return additionalAddresses;
    }

    /**
     * 🔥 ГЕНЕРАЦИЯ BIN ARRAY PDA ДЛЯ АНАЛИЗА
     */
    async generateBinArrayPDAs() {
        const binArrays = [];

        // Для Pool 1 (активный бин -4052, диапазон ±1)
        const pool1Bins = [-4053, -4052, -4051];
        for (const binId of pool1Bins) {
            const chunkId = Math.floor(binId / 64); // Chunk calculation
            const [binArrayPDA] = PublicKey.findProgramAddressSync([
                Buffer.from('bin_array'),
                this.POOL_1_ADDRESS.toBuffer(),
                Buffer.from(chunkId.toString())
            ], this.METEORA_DLMM_PROGRAM);
            binArrays.push(binArrayPDA);
        }

        // Для Pool 2 (активный бин -1621, диапазон ±1)
        const pool2Bins = [-1622, -1621, -1620];
        for (const binId of pool2Bins) {
            const chunkId = Math.floor(binId / 64);
            const [binArrayPDA] = PublicKey.findProgramAddressSync([
                Buffer.from('bin_array'),
                this.POOL_2_ADDRESS.toBuffer(),
                Buffer.from(chunkId.toString())
            ], this.METEORA_DLMM_PROGRAM);
            binArrays.push(binArrayPDA);
        }

        return [...new Set(binArrays.map(pda => pda.toString()))].map(addr => new PublicKey(addr));
    }

    /**
     * 🔥 ГЕНЕРАЦИЯ BITMAP EXTENSION PDA
     */
    async getBinArrayBitmapExtension(poolAddress) {
        const [bitmapExtension] = PublicKey.findProgramAddressSync([
            Buffer.from('bin_array_bitmap_extension'),
            poolAddress.toBuffer()
        ], this.METEORA_DLMM_PROGRAM);

        return bitmapExtension;
    }
}

// Запуск измерения
async function main() {
    const measurer = new FlashLoanInstructionMeasurer();
    await measurer.measureAllInstructions();

    // Анализ дополнительных адресов для ALT сжатия
    await measurer.analyzeAdditionalALTAddresses();
}

main().catch(console.error);
