/**
 * 🔬 ПРОДВИНУТЫЙ БРУТФОРС METEORA DLMM SEEDS
 * Учитывает bump байты, числовые параметры, разные порядки
 */

const { PublicKey } = require('@solana/web3.js');

console.log('🔬 ПРОДВИНУТЫЙ БРУТФОРС METEORA DLMM SEEDS\n');

class AdvancedSeedBruteforce {
    constructor() {
        this.METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        
        // Данные из успешной транзакции Kai-WSOL
        this.TARGET_DATA = {
            position: '9ZxeGGgXYsFykPhBkP5tizx5WDWB5QQJjzyahRwwTGxb',
            lbPair: 'E9kQAzGFvgC7xtgNGCe63meEKBFw2T5yNjzgbJAtNcSJ',
            binArrayBitmapExtension: 'GHhTdx41vXRhzUGxCAUhpYxsi2P5668JYW7R38fuxkDQ',
            user: 'H6uwL8TyV54xAUnarAwMLnRQtEpAdRDzoEjkfFbyF8vS',
            activeBinId: -485 // Из логов транзакции
        };
        
        // Наши данные для проверки
        this.OUR_DATA = {
            lbPair: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
            binArrayBitmapExtension: '59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li',
            position: 'Gbv33r6KGduHGWb8D5m4jhJMTqnGE4oaMun7ToHLr1UC',
            user: 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV'
        };
    }

    /**
     * 🔧 СОЗДАНИЕ ЧИСЛОВЫХ БУФЕРОВ (ИСПРАВЛЕНО ДЛЯ ОТРИЦАТЕЛЬНЫХ ЧИСЕЛ)
     */
    createNumericBuffers(number) {
        const buffers = {};

        // u8 - только положительные
        if (number >= 0 && number <= 255) {
            buffers.u8 = Buffer.from([number]);
        }

        // i32le - поддерживает отрицательные
        const i32Buffer = Buffer.alloc(4);
        i32Buffer.writeInt32LE(number, 0);
        buffers.i32le = i32Buffer;

        // u32le - только для положительных
        if (number >= 0) {
            const u32Buffer = Buffer.alloc(4);
            u32Buffer.writeUInt32LE(number, 0);
            buffers.u32le = u32Buffer;
        }

        // i64le - поддерживает отрицательные
        const i64Buffer = Buffer.alloc(8);
        i64Buffer.writeBigInt64LE(BigInt(number), 0);
        buffers.i64le = i64Buffer;

        return buffers;
    }

    /**
     * 🔍 БРУТФОРС POSITION SEEDS
     */
    bruteforcePositionSeeds() {
        console.log('🔍 ПРОДВИНУТЫЙ БРУТФОРС POSITION SEEDS');
        console.log('==========================================');
        
        const target = new PublicKey(this.TARGET_DATA.position);
        const lbPair = new PublicKey(this.TARGET_DATA.lbPair);
        const user = new PublicKey(this.TARGET_DATA.user);
        const binId = this.TARGET_DATA.activeBinId;
        
        console.log(`🎯 Целевой Position: ${target.toString()}`);
        console.log(`📊 LB Pair: ${lbPair.toString()}`);
        console.log(`👤 User: ${user.toString()}`);
        console.log(`🔢 Active Bin ID: ${binId}`);

        // Возможные строковые seeds
        const stringSeeds = [
            'position',
            'user_position', 
            'lb_position',
            'meteora_position',
            'dlmm_position',
            'pos'
        ];

        // Числовые буферы для bin ID
        const binIdBuffers = this.createNumericBuffers(binId);
        const binIdAbsBuffers = this.createNumericBuffers(Math.abs(binId));

        let foundSeeds = [];
        let testCount = 0;

        // Тестируем различные комбинации
        const testCombinations = [
            // Базовые комбинации
            { seeds: ['string', 'user', 'lbPair'], description: 'string + user + lbPair' },
            { seeds: ['string', 'lbPair', 'user'], description: 'string + lbPair + user' },
            { seeds: ['string', 'user'], description: 'string + user' },
            { seeds: ['string', 'lbPair'], description: 'string + lbPair' },
            
            // С bin ID
            { seeds: ['string', 'user', 'lbPair', 'binId'], description: 'string + user + lbPair + binId' },
            { seeds: ['string', 'lbPair', 'user', 'binId'], description: 'string + lbPair + user + binId' },
            { seeds: ['string', 'lbPair', 'binId'], description: 'string + lbPair + binId' },
            { seeds: ['string', 'user', 'binId'], description: 'string + user + binId' },
            
            // Без строки
            { seeds: ['user', 'lbPair'], description: 'user + lbPair' },
            { seeds: ['lbPair', 'user'], description: 'lbPair + user' },
            { seeds: ['user', 'lbPair', 'binId'], description: 'user + lbPair + binId' },
            { seeds: ['lbPair', 'binId'], description: 'lbPair + binId' }
        ];

        stringSeeds.forEach(stringSeed => {
            testCombinations.forEach(combo => {
                // Тестируем разные форматы bin ID
                const binIdVariants = [];

                if (binIdBuffers.i32le) binIdVariants.push({ name: 'i32le', buffer: binIdBuffers.i32le });
                if (binIdBuffers.u32le) binIdVariants.push({ name: 'u32le', buffer: binIdBuffers.u32le });
                if (binIdBuffers.i64le) binIdVariants.push({ name: 'i64le', buffer: binIdBuffers.i64le });
                if (binIdAbsBuffers.i32le) binIdVariants.push({ name: 'abs_i32le', buffer: binIdAbsBuffers.i32le });
                if (binIdBuffers.u8) binIdVariants.push({ name: 'u8', buffer: binIdBuffers.u8 });

                binIdVariants.forEach(binIdVariant => {
                    try {
                        const seedBuffers = [];
                        
                        combo.seeds.forEach(seedType => {
                            switch (seedType) {
                                case 'string':
                                    seedBuffers.push(Buffer.from(stringSeed));
                                    break;
                                case 'user':
                                    seedBuffers.push(user.toBuffer());
                                    break;
                                case 'lbPair':
                                    seedBuffers.push(lbPair.toBuffer());
                                    break;
                                case 'binId':
                                    seedBuffers.push(binIdVariant.buffer);
                                    break;
                            }
                        });

                        const [pda, bump] = PublicKey.findProgramAddressSync(seedBuffers, this.METEORA_DLMM_PROGRAM);
                        testCount++;

                        if (pda.equals(target)) {
                            const result = {
                                stringSeed,
                                combo: combo.description,
                                binIdFormat: binIdVariant.name,
                                seeds: seedBuffers,
                                bump,
                                pda: pda.toString()
                            };
                            foundSeeds.push(result);
                            
                            console.log(`\n🎉 НАЙДЕН POSITION SEED!`);
                            console.log(`   String: "${stringSeed}"`);
                            console.log(`   Комбинация: ${combo.description}`);
                            console.log(`   Bin ID формат: ${binIdVariant.name}`);
                            console.log(`   Bump: ${bump}`);
                            console.log(`   PDA: ${pda.toString()}`);
                        }

                        // Логируем прогресс каждые 100 тестов
                        if (testCount % 100 === 0) {
                            console.log(`   🔄 Протестировано: ${testCount} комбинаций...`);
                        }

                    } catch (error) {
                        // Игнорируем ошибки генерации PDA
                    }
                });
            });
        });

        console.log(`\n📊 РЕЗУЛЬТАТЫ БРУТФОРСА POSITION:`);
        console.log(`   Протестировано комбинаций: ${testCount}`);
        console.log(`   Найдено совпадений: ${foundSeeds.length}`);

        return foundSeeds;
    }

    /**
     * 🔍 БРУТФОРС BITMAP EXTENSION SEEDS
     */
    bruteForceBitmapExtensionSeeds() {
        console.log('\n🔍 ПРОДВИНУТЫЙ БРУТФОРС BITMAP EXTENSION SEEDS');
        console.log('==========================================');
        
        const target = new PublicKey(this.TARGET_DATA.binArrayBitmapExtension);
        const lbPair = new PublicKey(this.TARGET_DATA.lbPair);
        
        console.log(`🎯 Целевой Bitmap Extension: ${target.toString()}`);
        console.log(`📊 LB Pair: ${lbPair.toString()}`);

        const stringSeeds = [
            'bitmap',
            'bin_array_bitmap',
            'bin_array_bitmap_ext',
            'bin_array_bitmap_extension',
            'bitmap_extension',
            'extension',
            'bitmap_ext',
            'ba_bitmap',
            'dlmm_bitmap',
            'meteora_bitmap'
        ];

        let foundSeeds = [];
        let testCount = 0;

        // Простые комбинации
        stringSeeds.forEach(stringSeed => {
            try {
                const [pda, bump] = PublicKey.findProgramAddressSync([
                    Buffer.from(stringSeed),
                    lbPair.toBuffer()
                ], this.METEORA_DLMM_PROGRAM);
                
                testCount++;

                if (pda.equals(target)) {
                    const result = {
                        stringSeed,
                        seeds: [Buffer.from(stringSeed), lbPair.toBuffer()],
                        bump,
                        pda: pda.toString()
                    };
                    foundSeeds.push(result);
                    
                    console.log(`\n🎉 НАЙДЕН BITMAP EXTENSION SEED!`);
                    console.log(`   String: "${stringSeed}"`);
                    console.log(`   Seeds: ["${stringSeed}", lbPair]`);
                    console.log(`   Bump: ${bump}`);
                    console.log(`   PDA: ${pda.toString()}`);
                }
            } catch (error) {
                // Игнорируем ошибки
            }
        });

        // Тестируем обратный порядок
        stringSeeds.forEach(stringSeed => {
            try {
                const [pda, bump] = PublicKey.findProgramAddressSync([
                    lbPair.toBuffer(),
                    Buffer.from(stringSeed)
                ], this.METEORA_DLMM_PROGRAM);
                
                testCount++;

                if (pda.equals(target)) {
                    const result = {
                        stringSeed,
                        seeds: [lbPair.toBuffer(), Buffer.from(stringSeed)],
                        bump,
                        pda: pda.toString(),
                        note: 'Обратный порядок'
                    };
                    foundSeeds.push(result);
                    
                    console.log(`\n🎉 НАЙДЕН BITMAP EXTENSION SEED (ОБРАТНЫЙ ПОРЯДОК)!`);
                    console.log(`   String: "${stringSeed}"`);
                    console.log(`   Seeds: [lbPair, "${stringSeed}"]`);
                    console.log(`   Bump: ${bump}`);
                    console.log(`   PDA: ${pda.toString()}`);
                }
            } catch (error) {
                // Игнорируем ошибки
            }
        });

        console.log(`\n📊 РЕЗУЛЬТАТЫ БРУТФОРСА BITMAP EXTENSION:`);
        console.log(`   Протестировано комбинаций: ${testCount}`);
        console.log(`   Найдено совпадений: ${foundSeeds.length}`);

        return foundSeeds;
    }

    /**
     * 🧪 ПОЛНЫЙ АНАЛИЗ
     */
    runFullBruteforce() {
        console.log('🔬 ПОЛНЫЙ ПРОДВИНУТЫЙ БРУТФОРС METEORA DLMM SEEDS');
        console.log('==========================================\n');

        const positionSeeds = this.bruteforcePositionSeeds();
        const bitmapSeeds = this.bruteForceBitmapExtensionSeeds();

        console.log('\n🎯 ИТОГОВЫЕ РЕЗУЛЬТАТЫ:');
        console.log('==========================================');

        if (positionSeeds.length > 0 || bitmapSeeds.length > 0) {
            console.log(`🎉 SEEDS НАЙДЕНЫ!`);
            
            if (positionSeeds.length > 0) {
                console.log(`\n✅ POSITION SEEDS (${positionSeeds.length} найдено):`);
                positionSeeds.forEach((result, index) => {
                    console.log(`   ${index + 1}. "${result.stringSeed}" + ${result.combo} (${result.binIdFormat})`);
                });
            }
            
            if (bitmapSeeds.length > 0) {
                console.log(`\n✅ BITMAP EXTENSION SEEDS (${bitmapSeeds.length} найдено):`);
                bitmapSeeds.forEach((result, index) => {
                    console.log(`   ${index + 1}. "${result.stringSeed}" ${result.note || ''}`);
                });
            }

            return { positionSeeds, bitmapSeeds };
        } else {
            console.log(`❌ SEEDS НЕ НАЙДЕНЫ ДАЖЕ ПРОДВИНУТЫМ БРУТФОРСОМ!`);
            console.log(`\n🔧 ВОЗМОЖНЫЕ ПРИЧИНЫ:`);
            console.log(`1. Используются вложенные PDA (PDA внутри PDA)`);
            console.log(`2. Используются хэши или другие производные`);
            console.log(`3. Это не PDA, а обычные аккаунты`);
            console.log(`4. Используется другая программа для генерации`);
            
            return null;
        }
    }
}

// Запуск продвинутого брутфорса
const bruteforcer = new AdvancedSeedBruteforce();
const result = bruteforcer.runFullBruteforce();

if (result) {
    console.log(`\n📋 ГОТОВЫЕ ФОРМУЛЫ НАЙДЕНЫ!`);
    process.exit(0);
} else {
    console.log(`\n❌ ПРОДВИНУТЫЙ БРУТФОРС НЕ ДАЛ РЕЗУЛЬТАТОВ!`);
    console.log(`💡 РЕКОМЕНДАЦИЯ: Используем статические адреса как было запланировано.`);
    process.exit(1);
}
