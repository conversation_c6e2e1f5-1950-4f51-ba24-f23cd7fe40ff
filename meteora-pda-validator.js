/**
 * 🔧 УНИВЕРСАЛЬНЫЙ ВАЛИДАТОР METEORA PDA
 * Проверяет PDA без отправки транзакций через RPC
 */

const { PublicKey, Connection } = require('@solana/web3.js');
const BN = require('bn.js');

console.log('🔧 УНИВЕРСАЛЬНЫЙ ВАЛИДАТОР METEORA PDA\n');

class MeteoraePDAValidator {
    constructor() {
        this.METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        this.connection = new Connection('https://api.mainnet-beta.solana.com');
    }

    /**
     * 🎯 КАНОНИЧНЫЕ ФУНКЦИИ ГЕНЕРАЦИИ PDA (ИЗ ОФИЦИАЛЬНОГО SDK)
     */
    
    getPositionPDA(lbPair, user) {
        return PublicKey.findProgramAddressSync(
            [Buffer.from("position"), lbPair.toBuffer(), user.toBuffer()],
            this.METEORA_DLMM_PROGRAM
        );
    }

    getStrategyPDA(lbPair, user) {
        return PublicKey.findProgramAddressSync(
            [Buffer.from("strategy"), lbPair.toBuffer(), user.toBuffer()],
            this.METEORA_DLMM_PROGRAM
        );
    }

    getStrategyMetadataPDA(strategy) {
        return PublicKey.findProgramAddressSync(
            [Buffer.from("metadata"), strategy.toBuffer()],
            this.METEORA_DLMM_PROGRAM
        );
    }

    getBinArrayBitmapExtensionPDA(lbPair, binId) {
        // 🔧 ПРАВИЛЬНОЕ КОДИРОВАНИЕ: BN.js с toTwos для signed i64 LE
        const binIdBuffer = new BN(binId).toTwos(64).toArrayLike(Buffer, 'le', 8);
        return PublicKey.findProgramAddressSync(
            [Buffer.from("bin_array"), lbPair.toBuffer(), binIdBuffer],
            this.METEORA_DLMM_PROGRAM
        );
    }

    getLiquidityAccountPDA(user) {
        return PublicKey.findProgramAddressSync(
            [Buffer.from("liquidity_account"), user.toBuffer()],
            this.METEORA_DLMM_PROGRAM
        );
    }

    getGlobalStatePDA() {
        return PublicKey.findProgramAddressSync(
            [Buffer.from("global_state")],
            this.METEORA_DLMM_PROGRAM
        );
    }

    /**
     * 🔍 ПРОВЕРКА PDA ЧЕРЕЗ RPC
     */
    async validatePDA(pda, expectedOwner = null, description = 'PDA') {
        try {
            console.log(`🔍 Проверка ${description}: ${pda.toString()}`);
            
            const accountInfo = await this.connection.getAccountInfo(pda);
            
            if (!accountInfo) {
                console.log(`   ❌ Аккаунт не существует на блокчейне`);
                return { exists: false, validOwner: false, error: 'Account does not exist' };
            }

            console.log(`   ✅ Аккаунт существует`);
            console.log(`   📊 Размер данных: ${accountInfo.data.length} байт`);
            console.log(`   🏠 Owner: ${accountInfo.owner.toString()}`);

            // Проверка owner
            const expectedOwnerKey = expectedOwner || this.METEORA_DLMM_PROGRAM;
            const validOwner = accountInfo.owner.equals(expectedOwnerKey);
            
            if (validOwner) {
                console.log(`   ✅ Owner правильный (Meteora DLMM Program)`);
            } else {
                console.log(`   ❌ Owner неправильный! Ожидался: ${expectedOwnerKey.toString()}`);
                console.log(`   ❌ Это вызовет ошибку 3007 в транзакции!`);
            }

            return {
                exists: true,
                validOwner,
                owner: accountInfo.owner.toString(),
                dataLength: accountInfo.data.length,
                lamports: accountInfo.lamports
            };

        } catch (error) {
            console.log(`   ❌ Ошибка проверки: ${error.message}`);
            return { exists: false, validOwner: false, error: error.message };
        }
    }

    /**
     * 🎯 ПОЛНАЯ ВАЛИДАЦИЯ ДЛЯ add_liquidity2
     */
    async validateForAddLiquidity2(lbPair, user, binId, expectedAddresses = {}) {
        console.log('🎯 ПОЛНАЯ ВАЛИДАЦИЯ ДЛЯ add_liquidity2');
        console.log('==========================================');
        console.log(`📊 LB Pair: ${lbPair.toString()}`);
        console.log(`👤 User: ${user.toString()}`);
        console.log(`🔢 Bin ID: ${binId}`);

        // Генерируем все необходимые PDA
        const [positionPDA, positionBump] = this.getPositionPDA(lbPair, user);
        const [bitmapPDA, bitmapBump] = this.getBinArrayBitmapExtensionPDA(lbPair, binId);
        const [strategyPDA, strategyBump] = this.getStrategyPDA(lbPair, user);
        const [liquidityPDA, liquidityBump] = this.getLiquidityAccountPDA(user);

        console.log('\n📋 СГЕНЕРИРОВАННЫЕ PDA:');
        console.log('------------------------------------------');
        console.log(`Position PDA:        ${positionPDA.toString()} (bump: ${positionBump})`);
        console.log(`Bitmap Extension:    ${bitmapPDA.toString()} (bump: ${bitmapBump})`);
        console.log(`Strategy PDA:        ${strategyPDA.toString()} (bump: ${strategyBump})`);
        console.log(`Liquidity Account:   ${liquidityPDA.toString()} (bump: ${liquidityBump})`);

        // Сравнение с ожидаемыми адресами
        if (expectedAddresses.position) {
            const positionMatches = positionPDA.toString() === expectedAddresses.position;
            console.log(`\n🔍 СРАВНЕНИЕ С ОЖИДАЕМЫМИ АДРЕСАМИ:`);
            console.log(`Position: ${positionMatches ? '✅ СОВПАДАЕТ' : '❌ НЕ СОВПАДАЕТ'}`);
            if (!positionMatches) {
                console.log(`   Ожидался: ${expectedAddresses.position}`);
                console.log(`   Получили: ${positionPDA.toString()}`);
            }
        }

        if (expectedAddresses.bitmapExtension) {
            const bitmapMatches = bitmapPDA.toString() === expectedAddresses.bitmapExtension;
            console.log(`Bitmap Extension: ${bitmapMatches ? '✅ СОВПАДАЕТ' : '❌ НЕ СОВПАДАЕТ'}`);
            if (!bitmapMatches) {
                console.log(`   Ожидался: ${expectedAddresses.bitmapExtension}`);
                console.log(`   Получили: ${bitmapPDA.toString()}`);
            }
        }

        // Проверка через RPC
        console.log('\n🔍 ПРОВЕРКА ЧЕРЕЗ RPC:');
        console.log('------------------------------------------');

        const results = {
            position: await this.validatePDA(positionPDA, null, 'Position'),
            bitmapExtension: await this.validatePDA(bitmapPDA, null, 'Bitmap Extension'),
            strategy: await this.validatePDA(strategyPDA, null, 'Strategy'),
            liquidityAccount: await this.validatePDA(liquidityPDA, null, 'Liquidity Account')
        };

        // Итоговая оценка
        console.log('\n🎯 ИТОГОВАЯ ОЦЕНКА:');
        console.log('==========================================');

        const allExist = Object.values(results).every(r => r.exists);
        const allValidOwners = Object.values(results).every(r => r.validOwner);

        if (allExist && allValidOwners) {
            console.log('🎉 ВСЕ PDA ВАЛИДНЫ!');
            console.log('✅ Все аккаунты существуют');
            console.log('✅ Все owners правильные');
            console.log('✅ Готово для использования в add_liquidity2');
        } else {
            console.log('❌ НАЙДЕНЫ ПРОБЛЕМЫ:');
            if (!allExist) {
                console.log('   ❌ Некоторые аккаунты не существуют');
            }
            if (!allValidOwners) {
                console.log('   ❌ Некоторые owners неправильные (ошибка 3007)');
            }
        }

        return {
            pdas: { positionPDA, bitmapPDA, strategyPDA, liquidityPDA },
            bumps: { positionBump, bitmapBump, strategyBump, liquidityBump },
            validation: results,
            allValid: allExist && allValidOwners
        };
    }

    /**
     * 🔧 ПОИСК ПРАВИЛЬНОГО BIN ID
     */
    async findCorrectBinId(lbPair, expectedBitmapPDA, searchRange = [-5000, 5000]) {
        console.log('\n🔧 ПОИСК ПРАВИЛЬНОГО BIN ID');
        console.log('==========================================');
        console.log(`🎯 Целевой Bitmap PDA: ${expectedBitmapPDA}`);
        console.log(`📊 Диапазон поиска: ${searchRange[0]} до ${searchRange[1]}`);

        for (let binId = searchRange[0]; binId <= searchRange[1]; binId++) {
            try {
                const [bitmapPDA] = this.getBinArrayBitmapExtensionPDA(lbPair, binId);
                
                if (bitmapPDA.toString() === expectedBitmapPDA) {
                    console.log(`🎉 НАЙДЕН ПРАВИЛЬНЫЙ BIN ID: ${binId}`);
                    console.log(`   PDA: ${bitmapPDA.toString()}`);
                    return binId;
                }

                // Логируем прогресс каждые 1000 итераций
                if (binId % 1000 === 0) {
                    console.log(`   Проверено до bin ID: ${binId}...`);
                }

            } catch (error) {
                // Игнорируем ошибки генерации
            }
        }

        console.log(`❌ Bin ID не найден в диапазоне ${searchRange[0]} - ${searchRange[1]}`);
        return null;
    }
}

// Экспорт для использования в других модулях
module.exports = MeteoraePDAValidator;

// CLI интерфейс
async function runCLI() {
    const args = process.argv.slice(2);
    
    if (args.length < 3) {
        console.log(`
🔧 ИСПОЛЬЗОВАНИЕ:
node meteora-pda-validator.js <LB_PAIR> <USER> <BIN_ID> [EXPECTED_POSITION] [EXPECTED_BITMAP]

📋 ПРИМЕРЫ:
# Базовая проверка
node meteora-pda-validator.js E9kQAzGFvgC7xtgNGCe63meEKBFw2T5yNjzgbJAtNcSJ H6uwL8TyV54xAUnarAwMLnRQtEpAdRDzoEjkfFbyF8vS -485

# С проверкой ожидаемых адресов
node meteora-pda-validator.js E9kQAzGFvgC7xtgNGCe63meEKBFw2T5yNjzgbJAtNcSJ H6uwL8TyV54xAUnarAwMLnRQtEpAdRDzoEjkfFbyF8vS -485 9ZxeGGgXYsFykPhBkP5tizx5WDWB5QQJjzyahRwwTGxb GHhTdx41vXRhzUGxCAUhpYxsi2P5668JYW7R38fuxkDQ
`);
        process.exit(1);
    }

    const [lbPairStr, userStr, binIdStr, expectedPosition, expectedBitmap] = args;
    
    const validator = new MeteoraePDAValidator();
    const lbPair = new PublicKey(lbPairStr);
    const user = new PublicKey(userStr);
    const binId = parseInt(binIdStr);

    const expectedAddresses = {};
    if (expectedPosition) expectedAddresses.position = expectedPosition;
    if (expectedBitmap) expectedAddresses.bitmapExtension = expectedBitmap;

    const result = await validator.validateForAddLiquidity2(lbPair, user, binId, expectedAddresses);

    if (result.allValid) {
        console.log('\n🎉 ВАЛИДАЦИЯ УСПЕШНА! PDA готовы к использованию.');
        process.exit(0);
    } else {
        console.log('\n❌ ВАЛИДАЦИЯ НЕ ПРОШЛА! Требуется исправление.');
        process.exit(1);
    }
}

// Запуск CLI если файл вызван напрямую
if (require.main === module) {
    runCLI().catch(console.error);
}
