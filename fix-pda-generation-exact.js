/**
 * 🔧 ТОЧНОЕ ИСПРАВЛЕНИЕ ГЕНЕРАЦИИ PDA
 * Находим ТОЧНУЮ причину расхождения с SDK и исправляем!
 */

const { PublicKey } = require('@solana/web3.js');
const BN = require('bn.js');

console.log('🔧 ТОЧНОЕ ИСПРАВЛЕНИЕ ГЕНЕРАЦИИ PDA\n');

class ExactPDAFixer {
    constructor() {
        this.METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        
        // ТОЧНЫЕ ДАННЫЕ ИЗ SDK ПЕРЕХВАТА
        this.SDK_RESULTS = {
            // Kai-WSOL пул (из SDK перехвата)
            kaiWsol: {
                lbPair: 'E9kQAzGFvgC7xtgNGCe63meEKBFw2T5yNjzgbJAtNcSJ',
                expectedPDA: 'GHhTdx41vXRhzUGxCAUhpYxsi2P5668JYW7R38fuxkDQ',
                binId: -8, // Из SDK перехвата: 248,255,255,255,255,255,255,255
                binIdHex: 'f8ffffffffffffff' // Точный hex из SDK
            },
            // WSOL-USDC пул (наш)
            wsolUsdc: {
                lbPair: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
                expectedPDA: '59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li',
                binId: null // Неизвестен, нужно найти
            }
        };
    }

    /**
     * 🔍 ТЕСТИРОВАНИЕ РАЗЛИЧНЫХ СПОСОБОВ КОДИРОВАНИЯ BIN ID
     */
    testBinIdEncodings(binId) {
        console.log(`🔍 ТЕСТИРОВАНИЕ КОДИРОВАНИЯ BIN ID: ${binId}`);
        console.log('------------------------------------------');

        const encodings = {};

        try {
            // 1. BN.js little-endian (правильный для Solana)
            const bn = new BN(binId);
            encodings.bn_le = bn.toArrayLike(Buffer, 'le', 8);
            console.log(`   1. BN.js LE (8 байт): ${encodings.bn_le.toString('hex')}`);
        } catch (e) {
            console.log(`   1. BN.js LE: ❌ ОШИБКА - ${e.message}`);
        }

        try {
            // 2. BN.js big-endian (неправильный)
            const bn = new BN(binId);
            encodings.bn_be = bn.toArrayLike(Buffer, 'be', 8);
            console.log(`   2. BN.js BE (8 байт): ${encodings.bn_be.toString('hex')}`);
        } catch (e) {
            console.log(`   2. BN.js BE: ❌ ОШИБКА - ${e.message}`);
        }

        try {
            // 3. Buffer.alloc + writeBigInt64LE (наш текущий метод)
            const buffer = Buffer.alloc(8);
            buffer.writeBigInt64LE(BigInt(binId), 0);
            encodings.bigint_le = buffer;
            console.log(`   3. BigInt LE (8 байт): ${encodings.bigint_le.toString('hex')}`);
        } catch (e) {
            console.log(`   3. BigInt LE: ❌ ОШИБКА - ${e.message}`);
        }

        try {
            // 4. Buffer.alloc + writeInt32LE (4 байта)
            const buffer = Buffer.alloc(4);
            buffer.writeInt32LE(binId, 0);
            encodings.int32_le = buffer;
            console.log(`   4. Int32 LE (4 байта): ${encodings.int32_le.toString('hex')}`);
        } catch (e) {
            console.log(`   4. Int32 LE: ❌ ОШИБКА - ${e.message}`);
        }

        try {
            // 5. Прямой hex из SDK (если есть)
            if (binId === -8) {
                encodings.sdk_hex = Buffer.from('f8ffffffffffffff', 'hex');
                console.log(`   5. SDK Hex (8 байт): ${encodings.sdk_hex.toString('hex')}`);
            }
        } catch (e) {
            console.log(`   5. SDK Hex: ❌ ОШИБКА - ${e.message}`);
        }

        return encodings;
    }

    /**
     * 🔍 ТЕСТИРОВАНИЕ РАЗЛИЧНЫХ ПОРЯДКОВ SEEDS
     */
    testSeedOrders(lbPair, binIdBuffer) {
        console.log(`🔍 ТЕСТИРОВАНИЕ ПОРЯДКОВ SEEDS`);
        console.log('------------------------------------------');

        const lbPairBuffer = new PublicKey(lbPair).toBuffer();
        const binArrayBuffer = Buffer.from("bin_array");

        const orders = [
            {
                name: 'SDK порядок: bin_array + lbPair + binId',
                seeds: [binArrayBuffer, lbPairBuffer, binIdBuffer]
            },
            {
                name: 'Альтернатива 1: lbPair + bin_array + binId',
                seeds: [lbPairBuffer, binArrayBuffer, binIdBuffer]
            },
            {
                name: 'Альтернатива 2: binId + bin_array + lbPair',
                seeds: [binIdBuffer, binArrayBuffer, lbPairBuffer]
            },
            {
                name: 'Альтернатива 3: bin_array + binId + lbPair',
                seeds: [binArrayBuffer, binIdBuffer, lbPairBuffer]
            },
            {
                name: 'Альтернатива 4: lbPair + binId + bin_array',
                seeds: [lbPairBuffer, binIdBuffer, binArrayBuffer]
            },
            {
                name: 'Альтернатива 5: binId + lbPair + bin_array',
                seeds: [binIdBuffer, lbPairBuffer, binArrayBuffer]
            }
        ];

        const results = [];

        orders.forEach((order, index) => {
            try {
                const [pda, bump] = PublicKey.findProgramAddressSync(order.seeds, this.METEORA_DLMM_PROGRAM);
                results.push({
                    index: index + 1,
                    name: order.name,
                    pda: pda.toString(),
                    bump,
                    success: true
                });
                console.log(`   ${index + 1}. ${order.name}`);
                console.log(`      PDA: ${pda.toString()}`);
                console.log(`      Bump: ${bump}`);
            } catch (error) {
                results.push({
                    index: index + 1,
                    name: order.name,
                    error: error.message,
                    success: false
                });
                console.log(`   ${index + 1}. ${order.name}: ❌ ОШИБКА - ${error.message}`);
            }
        });

        return results;
    }

    /**
     * 🎯 ПОИСК ТОЧНОГО СОВПАДЕНИЯ ДЛЯ KAI-WSOL
     */
    findExactMatchForKaiWsol() {
        console.log('🎯 ПОИСК ТОЧНОГО СОВПАДЕНИЯ ДЛЯ KAI-WSOL');
        console.log('==========================================');

        const target = this.SDK_RESULTS.kaiWsol;
        console.log(`Цель: ${target.expectedPDA}`);
        console.log(`LB Pair: ${target.lbPair}`);
        console.log(`Bin ID: ${target.binId}`);

        // Тестируем различные кодирования bin ID
        const encodings = this.testBinIdEncodings(target.binId);

        let foundMatch = null;

        // Для каждого кодирования тестируем все порядки seeds
        Object.entries(encodings).forEach(([encodingName, binIdBuffer]) => {
            console.log(`\n📊 ТЕСТИРОВАНИЕ КОДИРОВАНИЯ: ${encodingName}`);
            console.log(`   Bin ID Buffer: ${binIdBuffer.toString('hex')}`);

            const orderResults = this.testSeedOrders(target.lbPair, binIdBuffer);

            // Проверяем совпадения
            orderResults.forEach(result => {
                if (result.success && result.pda === target.expectedPDA) {
                    foundMatch = {
                        encoding: encodingName,
                        order: result.name,
                        pda: result.pda,
                        bump: result.bump,
                        binIdBuffer: binIdBuffer.toString('hex')
                    };
                    console.log(`      🎉 НАЙДЕНО СОВПАДЕНИЕ!`);
                    console.log(`         Кодирование: ${encodingName}`);
                    console.log(`         Порядок: ${result.name}`);
                    console.log(`         PDA: ${result.pda}`);
                    console.log(`         Bump: ${result.bump}`);
                }
            });
        });

        return foundMatch;
    }

    /**
     * 🔧 ПРИМЕНЕНИЕ НАЙДЕННОЙ ФОРМУЛЫ К WSOL-USDC
     */
    applyFormulaToWsolUsdc(correctFormula) {
        console.log('\n🔧 ПРИМЕНЕНИЕ НАЙДЕННОЙ ФОРМУЛЫ К WSOL-USDC');
        console.log('==========================================');

        if (!correctFormula) {
            console.log('❌ Нет правильной формулы для применения!');
            return null;
        }

        const wsolUsdcTarget = this.SDK_RESULTS.wsolUsdc;
        console.log(`Цель: ${wsolUsdcTarget.expectedPDA}`);
        console.log(`LB Pair: ${wsolUsdcTarget.lbPair}`);
        console.log(`Формула: ${correctFormula.encoding} + ${correctFormula.order}`);

        // Пробуем разные bin ID для WSOL-USDC
        const binIdsToTry = [
            0, -1, 1, -8, -9, -4517, -4518, -4516, -4515, -4519,
            -69, -70, -68, -71, -67, -485, -486, -484, -487, -483,
            -100, 100, -200, 200, -500, 500, -1000, 1000
        ];

        let foundWsolUsdcMatch = null;

        binIdsToTry.forEach(binId => {
            try {
                // Применяем найденную формулу
                let binIdBuffer;
                
                if (correctFormula.encoding === 'bn_le') {
                    binIdBuffer = new BN(binId).toArrayLike(Buffer, 'le', 8);
                } else if (correctFormula.encoding === 'bigint_le') {
                    binIdBuffer = Buffer.alloc(8);
                    binIdBuffer.writeBigInt64LE(BigInt(binId), 0);
                } else if (correctFormula.encoding === 'int32_le') {
                    binIdBuffer = Buffer.alloc(4);
                    binIdBuffer.writeInt32LE(binId, 0);
                } else {
                    return; // Пропускаем неизвестные кодирования
                }

                // Применяем правильный порядок seeds
                let seeds;
                const lbPairBuffer = new PublicKey(wsolUsdcTarget.lbPair).toBuffer();
                const binArrayBuffer = Buffer.from("bin_array");

                if (correctFormula.order.includes('bin_array + lbPair + binId')) {
                    seeds = [binArrayBuffer, lbPairBuffer, binIdBuffer];
                } else if (correctFormula.order.includes('lbPair + bin_array + binId')) {
                    seeds = [lbPairBuffer, binArrayBuffer, binIdBuffer];
                } else if (correctFormula.order.includes('binId + bin_array + lbPair')) {
                    seeds = [binIdBuffer, binArrayBuffer, lbPairBuffer];
                } else if (correctFormula.order.includes('bin_array + binId + lbPair')) {
                    seeds = [binArrayBuffer, binIdBuffer, lbPairBuffer];
                } else if (correctFormula.order.includes('lbPair + binId + bin_array')) {
                    seeds = [lbPairBuffer, binIdBuffer, binArrayBuffer];
                } else if (correctFormula.order.includes('binId + lbPair + bin_array')) {
                    seeds = [binIdBuffer, lbPairBuffer, binArrayBuffer];
                } else {
                    return; // Пропускаем неизвестные порядки
                }

                const [pda, bump] = PublicKey.findProgramAddressSync(seeds, this.METEORA_DLMM_PROGRAM);

                if (pda.toString() === wsolUsdcTarget.expectedPDA) {
                    foundWsolUsdcMatch = {
                        binId,
                        pda: pda.toString(),
                        bump,
                        encoding: correctFormula.encoding,
                        order: correctFormula.order
                    };
                    console.log(`🎉 НАЙДЕН BIN ID ДЛЯ WSOL-USDC: ${binId}`);
                    console.log(`   PDA: ${pda.toString()}`);
                    console.log(`   Bump: ${bump}`);
                }

            } catch (error) {
                // Игнорируем ошибки
            }
        });

        return foundWsolUsdcMatch;
    }

    /**
     * 🚀 ПОЛНЫЙ АНАЛИЗ И ИСПРАВЛЕНИЕ
     */
    runFullFix() {
        console.log('🚀 ПОЛНЫЙ АНАЛИЗ И ИСПРАВЛЕНИЕ ГЕНЕРАЦИИ PDA');
        console.log('==========================================\n');

        // Шаг 1: Найти точную формулу для Kai-WSOL
        const correctFormula = this.findExactMatchForKaiWsol();

        if (!correctFormula) {
            console.log('\n❌ НЕ УДАЛОСЬ НАЙТИ ПРАВИЛЬНУЮ ФОРМУЛУ!');
            console.log('🔧 РЕКОМЕНДАЦИИ:');
            console.log('   1. Проверить что SDK перехват дал правильные данные');
            console.log('   2. Возможно нужны другие варианты кодирования');
            console.log('   3. Возможно нужны другие порядки seeds');
            return null;
        }

        console.log('\n🎉 НАЙДЕНА ПРАВИЛЬНАЯ ФОРМУЛА!');
        console.log('==========================================');
        console.log(`✅ Кодирование: ${correctFormula.encoding}`);
        console.log(`✅ Порядок seeds: ${correctFormula.order}`);
        console.log(`✅ Bin ID Buffer: ${correctFormula.binIdBuffer}`);
        console.log(`✅ PDA: ${correctFormula.pda}`);
        console.log(`✅ Bump: ${correctFormula.bump}`);

        // Шаг 2: Применить формулу к WSOL-USDC
        const wsolUsdcResult = this.applyFormulaToWsolUsdc(correctFormula);

        console.log('\n📋 ИТОГОВЫЕ РЕЗУЛЬТАТЫ:');
        console.log('==========================================');

        if (wsolUsdcResult) {
            console.log(`🎉 ПОЛНЫЙ УСПЕХ!`);
            console.log(`✅ Kai-WSOL: Bin ID ${this.SDK_RESULTS.kaiWsol.binId} → ${correctFormula.pda}`);
            console.log(`✅ WSOL-USDC: Bin ID ${wsolUsdcResult.binId} → ${wsolUsdcResult.pda}`);
            
            console.log('\n📋 ПРАВИЛЬНАЯ ФУНКЦИЯ ДЛЯ ИНТЕГРАЦИИ:');
            this.generateCorrectFunction(correctFormula);
            
        } else {
            console.log(`⚠️ ЧАСТИЧНЫЙ УСПЕХ`);
            console.log(`✅ Kai-WSOL: формула найдена`);
            console.log(`❌ WSOL-USDC: bin ID не найден среди тестируемых значений`);
            console.log(`🔧 Нужно расширить диапазон поиска bin ID`);
        }

        return { correctFormula, wsolUsdcResult };
    }

    /**
     * 📋 ГЕНЕРАЦИЯ ПРАВИЛЬНОЙ ФУНКЦИИ
     */
    generateCorrectFunction(formula) {
        console.log('==========================================');
        
        let encodingCode;
        if (formula.encoding === 'bn_le') {
            encodingCode = 'new BN(binId).toArrayLike(Buffer, "le", 8)';
        } else if (formula.encoding === 'bigint_le') {
            encodingCode = `
    const buffer = Buffer.alloc(8);
    buffer.writeBigInt64LE(BigInt(binId), 0);
    return buffer;`;
        } else if (formula.encoding === 'int32_le') {
            encodingCode = `
    const buffer = Buffer.alloc(4);
    buffer.writeInt32LE(binId, 0);
    return buffer;`;
        }

        let seedsCode;
        if (formula.order.includes('bin_array + lbPair + binId')) {
            seedsCode = '[Buffer.from("bin_array"), lbPairAddress.toBuffer(), binIdBuffer]';
        } else if (formula.order.includes('lbPair + bin_array + binId')) {
            seedsCode = '[lbPairAddress.toBuffer(), Buffer.from("bin_array"), binIdBuffer]';
        } else {
            seedsCode = '/* Определить правильный порядок */';
        }

        console.log(`
// 🎯 ПРАВИЛЬНАЯ ФУНКЦИЯ getBinArrayBitmapExtension (НАЙДЕНА ЧЕРЕЗ ТОЧНЫЙ АНАЛИЗ!)
getBinArrayBitmapExtension(lbPairAddress, binId = 0) {
    // Правильное кодирование bin ID (${formula.encoding})
    const binIdBuffer = ${encodingCode};
    
    // Правильный порядок seeds
    const seeds = ${seedsCode};
    
    const [pda, bump] = PublicKey.findProgramAddressSync(seeds, this.METEORA_DLMM_PROGRAM);
    return pda;
}
`);
    }
}

// Запуск полного исправления
const fixer = new ExactPDAFixer();
const result = fixer.runFullFix();

if (result && result.correctFormula) {
    console.log(`\n🎉 ИСПРАВЛЕНИЕ ЗАВЕРШЕНО УСПЕШНО!`);
    console.log(`✅ Найдена точная формула генерации PDA`);
    console.log(`✅ Готово к интеграции в основной код`);
    process.exit(0);
} else {
    console.log(`\n❌ ИСПРАВЛЕНИЕ НЕ ЗАВЕРШЕНО!`);
    console.log(`❌ Требуется дополнительный анализ`);
    process.exit(1);
}
