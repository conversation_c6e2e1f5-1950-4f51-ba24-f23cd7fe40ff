/**
 * 🔧 КОД ДЛЯ ИНТЕГРАЦИИ В complete-flash-loan-structure.js
 * Замените существующие функции на эти каноничные версии
 */

// 🎯 КАНОНИЧНАЯ ФУНКЦИЯ getBinArrayBitmapExtension (ИСПРАВЛЕННАЯ!)
async getBinArrayBitmapExtension(poolAddress, binId = -4517) {
    try {
        console.log(`🔧 getBinArrayBitmapExtension: ${poolAddress.toString().slice(0,8)}..., binId=${binId}`);

        // 🔧 КАНОНИЧНОЕ КОДИРОВАНИЕ: BN.js с toTwos для signed i64 LE
        const binIdBuffer = new BN(binId).toTwos(64).toArrayLike(Buffer, 'le', 8);
        const seeds = [
            Buffer.from("bin_array"),           // Seed 1: строка "bin_array"
            poolAddress.toBuffer(),             // Seed 2: LB Pair address (32 байта)
            binIdBuffer                         // Seed 3: Bin ID как signed i64 LE (BN.js)
        ];

        const [pda, bump] = PublicKey.findProgramAddressSync(seeds, this.METEORA_DLMM_PROGRAM);

        console.log(`   ✅ Bitmap Extension PDA (каноничная формула): ${pda.toString()}`);
        console.log(`   ✅ Bump: ${bump}`);
        
        return pda;

    } catch (error) {
        console.error(`❌ КРИТИЧЕСКАЯ ОШИБКА в getBinArrayBitmapExtension: ${error.message}`);
        throw error;
    }
}

// 🎯 КАНОНИЧНАЯ ФУНКЦИЯ getPositionPDA
getPositionPDA(lbPair, user) {
    return PublicKey.findProgramAddressSync(
        [Buffer.from("position"), lbPair.toBuffer(), user.toBuffer()],
        this.METEORA_DLMM_PROGRAM
    );
}

// 🔧 ИЗВЕСТНЫЕ BIN ID ДЛЯ ПУЛОВ
const KNOWN_BIN_IDS = {
    '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6': -4517, // WSOL-USDC
    'E9kQAzGFvgC7xtgNGCe63meEKBFw2T5yNjzgbJAtNcSJ': -8  // Kai-WSOL (из SDK перехвата)
};
