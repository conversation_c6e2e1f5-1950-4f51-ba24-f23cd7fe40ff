/**
 * 🌐 ЦЕНТРАЛИЗОВАННЫЙ RPC МЕНЕДЖЕР
 * Единая система управления RPC подключениями с fallback и retry логикой
 */

const { Connection } = require('@solana/web3.js');

class CentralizedRPCManager {
    constructor() {
        // 🔥 ТРАНЗАКЦИИ ТОЛЬКО ЧЕРЕЗ QUICKNODE, ДАННЫЕ ЧЕРЕЗ ВСЕ!
        this.rpcEndpoints = [
            // Helius Transaction - ТОЛЬКО ДЛЯ ТРАНЗАКЦИЙ (НЕ SOLANA OFFICIAL!)
            {
                url: process.env.QUICKNODE_RPC_URL, // На самом деле Helius
                name: 'He<PERSON> Transaction',
                type: 'transaction', // ТОЛЬКО ТРАНЗАКЦИИ!
                maxRequestsPerSecond: 10,
                timeout: 10000,
                priority: 1
            },
            // Helius Primary - ТОЛЬКО ДЛЯ ДАННЫХ
            {
                url: process.env.HELIUS_RPC_URL,
                name: 'He<PERSON> Primary',
                type: 'data', // ТОЛЬКО ДАННЫЕ!
                maxRequestsPerSecond: 10,
                timeout: 8000,
                priority: 2
            },
            // 🚫 HELIUS SECONDARY И TERTIARY ОТКЛЮЧЕНЫ - НЕТ ДОПОЛНИТЕЛЬНЫХ КЛЮЧЕЙ
            // {
            //     url: process.env.HELIUS_RPC_URL_2,
            //     name: 'Helius Secondary',
            //     type: 'data',
            //     maxRequestsPerSecond: 10,
            //     timeout: 8000,
            //     priority: 3
            // },
            // {
            //     url: process.env.HELIUS_RPC_URL_3,
            //     name: 'Helius Tertiary',
            //     type: 'data',
            //     maxRequestsPerSecond: 10,
            //     timeout: 8000,
            //     priority: 4
            // },
            // Solana Official RPC - ТОЛЬКО ДЛЯ ДАННЫХ
            {
                url: 'https://api.mainnet-beta.solana.com',
                name: 'Solana Official',
                type: 'data', // ТОЛЬКО ДАННЫЕ!
                maxRequestsPerSecond: 10,
                timeout: 8000,
                priority: 5
            }
        ].filter(endpoint => endpoint.url && endpoint.url.startsWith('http')); // Убираем undefined и невалидные URLs

        // 🔥 СИСТЕМА РАВНОМЕРНОГО РАСПРЕДЕЛЕНИЯ НАГРУЗКИ
        this.loadBalancer = {
            // Все Helius endpoints для транзакций И данных
            transactionEndpoints: this.rpcEndpoints.filter(ep => ep.type === 'both' || ep.type === 'transaction'),
            dataEndpoints: this.rpcEndpoints.filter(ep => ep.type === 'both' || ep.type === 'data'),
            fallbackEndpoints: this.rpcEndpoints.filter(ep => ep.type === 'fallback'),

            // Round-robin индексы для каждого типа
            transactionIndex: 0,
            dataIndex: 0,
            fallbackIndex: 0,

            // Счетчики запросов для rate limiting
            requestCounts: new Map(),
            lastResetTime: Date.now()
        };

        this.currentEndpointIndex = 0;
        this.connection = null;
        this.connectionStats = new Map();
        this.connections = new Map(); // Кэш подключений для каждого endpoint
        this.lastHealthCheck = 0;
        this.healthCheckInterval = 30000; // 30 секунд

        // Инициализируем статистику и счетчики
        this.rpcEndpoints.forEach(endpoint => {
            this.connectionStats.set(endpoint.url, {
                successCount: 0,
                errorCount: 0,
                lastError: null,
                lastSuccess: null,
                isHealthy: true,
                averageResponseTime: 0,
                type: endpoint.type
            });

            // Инициализируем счетчики запросов
            this.loadBalancer.requestCounts.set(endpoint.url, {
                count: 0,
                lastReset: Date.now()
            });
        });

        console.log(`🔥 ТРАНЗАКЦИИ ЧЕРЕЗ QUICKNODE, ДАННЫЕ ЧЕРЕЗ ВСЕ:`);
        console.log(`   📤 Транзакции: ${this.loadBalancer.transactionEndpoints.length} endpoint (ТОЛЬКО QuickNode)`);
        console.log(`   📊 Данные: ${this.loadBalancer.dataEndpoints.length} endpoints (3 Helius + 1 Solana)`);
        console.log(`   🚫 Fallback: ${this.loadBalancer.fallbackEndpoints.length} endpoints (нет fallback)`);

        // Отладочная информация
        console.log(`\n🔍 УНИКАЛЬНЫЕ ENDPOINTS:`);
        console.log(`   Всего endpoints: ${this.rpcEndpoints.length}`);
        this.rpcEndpoints.forEach((ep, i) => {
            const urlStatus = ep.url ? 'URL OK' : 'NO URL';
            const urlPreview = ep.url ? ep.url.substring(0, 50) + '...' : 'UNDEFINED';
            console.log(`   ${i + 1}. ${ep.name} (${ep.type}) - ${urlStatus}`);
            console.log(`      URL: ${urlPreview}`);
        });

        console.log(`\n🔍 ОТЛАДКА ПЕРЕМЕННЫХ ОКРУЖЕНИЯ:`);
        console.log(`   HELIUS_RPC_URL: ${process.env.HELIUS_RPC_URL ? 'ЕСТЬ' : 'НЕТ'}`);
        console.log(`   HELIUS_RPC_URL_2: ${process.env.HELIUS_RPC_URL_2 ? 'ЕСТЬ' : 'НЕТ'}`);
        console.log(`   HELIUS_RPC_URL_3: ${process.env.HELIUS_RPC_URL_3 ? 'ЕСТЬ' : 'НЕТ'}`);
    }

    /**
     * 🔥 ПОЛУЧЕНИЕ ПОДКЛЮЧЕНИЯ ПО ТИПУ ЗАПРОСА (РАВНОМЕРНОЕ РАСПРЕДЕЛЕНИЕ)
     */
    async getConnectionByType(requestType = 'data') {
        // 🔥 ПРЯМОЕ ПОДКЛЮЧЕНИЕ БЕЗ ПРОВЕРОК И FALLBACK!
        const url = process.env.QUICKNODE_RPC_URL || process.env.HELIUS_RPC_URL;

        if (!url) {
            throw new Error('❌ НЕТ RPC URL В ПЕРЕМЕННЫХ ОКРУЖЕНИЯ!');
        }

        console.log(`🔗 ПРЯМОЕ ПОДКЛЮЧЕНИЕ: ${url.slice(0, 30)}...`);

        // 🔥 СОЗДАЕМ ПОДКЛЮЧЕНИЕ НАПРЯМУЮ БЕЗ ПРОВЕРОК!
        return new Connection(url, {
            commitment: 'confirmed',
            confirmTransactionInitialTimeout: 30000,
            disableRetryOnRateLimit: true,
            maxSupportedTransactionVersion: 0
        });
    }

    /**
     * 🔗 ПОЛУЧЕНИЕ АКТИВНОГО ПОДКЛЮЧЕНИЯ (LEGACY МЕТОД)
     */
    async getConnection() {
        return this.getConnectionByType('data');
    }

    /**
     * 📤 ПОЛУЧЕНИЕ СЛЕДУЮЩЕГО ENDPOINT ДЛЯ ТРАНЗАКЦИЙ (HELIUS)
     */
    getNextTransactionEndpoint() {
        const endpoints = this.loadBalancer.transactionEndpoints; // Убираем фильтр здоровья

        if (endpoints.length === 0) return null;

        const currentIndex = this.loadBalancer.transactionIndex % endpoints.length;
        const endpoint = endpoints[currentIndex];
        this.loadBalancer.transactionIndex++;

        console.log(`📤 Транзакция через: ${endpoint.name} (индекс: ${currentIndex}/${endpoints.length})`);
        return endpoint;
    }

    /**
     * 📊 ПОЛУЧЕНИЕ СЛЕДУЮЩЕГО ENDPOINT ДЛЯ ДАННЫХ (РАВНОМЕРНОЕ РАСПРЕДЕЛЕНИЕ)
     */
    getNextDataEndpoint() {
        const endpoints = this.loadBalancer.dataEndpoints; // Убираем фильтр здоровья

        if (endpoints.length === 0) return null;

        const currentIndex = this.loadBalancer.dataIndex % endpoints.length;
        const endpoint = endpoints[currentIndex];
        this.loadBalancer.dataIndex++;

        console.log(`📊 Данные через: ${endpoint.name} (индекс: ${currentIndex}/${endpoints.length})`);
        return endpoint;
    }

    /**
     * 🔄 ПОЛУЧЕНИЕ FALLBACK ENDPOINT (SOLANA OFFICIAL)
     */
    getNextFallbackEndpoint() {
        const endpoints = this.loadBalancer.fallbackEndpoints.filter(ep =>
            this.connectionStats.get(ep.url).isHealthy
        );

        if (endpoints.length === 0) return null;

        const endpoint = endpoints[this.loadBalancer.fallbackIndex % endpoints.length];
        this.loadBalancer.fallbackIndex++;

        console.log(`🔄 Fallback через: ${endpoint.name}`);
        return endpoint;
    }

    /**
     * ⏱️ ПРОВЕРКА RATE LIMIT
     */
    checkRateLimit(endpoint) {
        const now = Date.now();
        const requestData = this.loadBalancer.requestCounts.get(endpoint.url);

        // Сброс счетчика каждую секунду
        if (now - requestData.lastReset > 1000) {
            requestData.count = 0;
            requestData.lastReset = now;
        }

        // Проверяем лимит
        if (requestData.count >= endpoint.maxRequestsPerSecond) {
            return false;
        }

        requestData.count++;
        return true;
    }

    /**
     * 🔗 ПОЛУЧЕНИЕ ИЛИ СОЗДАНИЕ ПОДКЛЮЧЕНИЯ (ОТКЛЮЧЕНО!)
     */
    async getOrCreateConnection(endpoint) {
        // 🔥 НЕ ИСПОЛЬЗУЕТСЯ - getConnectionByType СОЗДАЕТ ПОДКЛЮЧЕНИЕ НАПРЯМУЮ!
        console.log(`⚠️ getOrCreateConnection НЕ ДОЛЖЕН ВЫЗЫВАТЬСЯ!`);
        throw new Error('getOrCreateConnection отключен - используйте getConnectionByType!');
    }

    /**
     * 🏥 ПРОВЕРКА ЗДОРОВЬЯ ПОДКЛЮЧЕНИЯ (ОТКЛЮЧЕНА!)
     */
    async isConnectionHealthy(connection = this.connection) {
        // 🔥 ВСЕГДА ВОЗВРАЩАЕМ true - НЕТ ПРОВЕРОК!
        console.log(`✅ Проверка здоровья отключена - считаем подключение здоровым`);
        return true;
    }

    /**
     * 📤 ОТПРАВКА ТРАНЗАКЦИИ ТОЛЬКО ЧЕРЕЗ QUICKNODE
     */
    async sendTransaction(transaction, options = {}) {
        const connection = await this.getConnectionByType('transaction');

        try {
            const signature = await connection.sendRawTransaction(transaction, {
                skipPreflight: true,
                maxRetries: 3,
                ...options
            });

            console.log(`📤 Транзакция отправлена через QuickNode: ${signature}`);
            return signature;

        } catch (error) {
            console.error(`❌ Ошибка отправки транзакции через QuickNode:`, error.message);
            throw error;
        }
    }

    /**
     * 🔄 ВЫПОЛНЕНИЕ RPC ЗАПРОСА С RETRY ЛОГИКОЙ И ТИПОМ
     */
    async executeWithRetry(operation, requestType = 'data', maxRetries = 3) {
        let lastError;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const connection = await this.getConnectionByType(requestType);
                const result = await operation(connection);

                return result;

            } catch (error) {
                lastError = error;

                if (error.message.includes('429') || error.message.includes('rate limit')) {
                    console.log(`⚠️ Rate limit на попытке ${attempt}, переключаемся на другой endpoint...`);
                    // Не ждем при rate limit, сразу переключаемся
                    continue;
                }

                if (attempt < maxRetries) {
                    console.log(`❌ RPC запрос провалился (попытка ${attempt}/${maxRetries}): ${error.message}`);
                    // Ждем перед повторной попыткой для других ошибок
                    await new Promise(resolve => setTimeout(resolve, attempt * 1000));
                }
            }
        }

        throw new Error(`RPC запрос провалился после ${maxRetries} попыток: ${lastError.message}`);
    }

    /**
     * 📊 ПОЛУЧЕНИЕ СТАТИСТИКИ RPC
     */
    getStats() {
        const stats = {
            currentEndpoint: this.rpcEndpoints[this.currentEndpointIndex],
            totalEndpoints: this.rpcEndpoints.length,
            healthyEndpoints: 0,
            endpoints: []
        };

        this.rpcEndpoints.forEach(endpoint => {
            const endpointStats = this.connectionStats.get(endpoint.url);
            const endpointInfo = {
                name: endpoint.name,
                url: endpoint.url.slice(0, 50) + '...',
                priority: endpoint.priority,
                isHealthy: endpointStats.isHealthy,
                successCount: endpointStats.successCount,
                errorCount: endpointStats.errorCount,
                averageResponseTime: Math.round(endpointStats.averageResponseTime),
                lastSuccess: endpointStats.lastSuccess ? new Date(endpointStats.lastSuccess).toISOString() : null,
                lastError: endpointStats.lastError
            };

            if (endpointStats.isHealthy) {
                stats.healthyEndpoints++;
            }

            stats.endpoints.push(endpointInfo);
        });

        return stats;
    }

    /**
     * 🔧 ПРИНУДИТЕЛЬНОЕ ПЕРЕКЛЮЧЕНИЕ НА СЛЕДУЮЩИЙ ENDPOINT
     */
    switchToNextEndpoint() {
        this.currentEndpointIndex = (this.currentEndpointIndex + 1) % this.rpcEndpoints.length;
        this.connection = null;
        console.log(`🔄 Переключение на: ${this.rpcEndpoints[this.currentEndpointIndex].name}`);
    }

    /**
     * 🔥 ПОЛУЧЕНИЕ LATEST BLOCKHASH ЧЕРЕЗ ЦЕНТРАЛИЗОВАННЫЙ RPC МЕНЕДЖЕР
     */
    async getLatestBlockhash(commitment = 'finalized') {
        const connection = await this.getConnection();
        if (!connection) {
            throw new Error('Нет доступного RPC подключения для получения blockhash');
        }

        try {
            const result = await connection.getLatestBlockhash(commitment);
            return result;
        } catch (error) {
            // Пробуем следующий endpoint
            this.switchToNextEndpoint();
            const nextConnection = await this.getConnection();
            if (nextConnection) {
                return await nextConnection.getLatestBlockhash(commitment);
            }

            throw new Error(`Не удалось получить blockhash ни через один RPC endpoint: ${error.message}`);
        }
    }

    /**
     * 📈 ВЫВОД СТАТИСТИКИ В КОНСОЛЬ
     */
    logStats() {
        const stats = this.getStats();
        console.log(`\n📊 RPC СТАТИСТИКА:`);
        console.log(`   🎯 Текущий: ${stats.currentEndpoint.name}`);
        console.log(`   ✅ Здоровых: ${stats.healthyEndpoints}/${stats.totalEndpoints}`);

        stats.endpoints.forEach((endpoint, index) => {
            const status = endpoint.isHealthy ? '✅' : '❌';
            const successRate = endpoint.successCount + endpoint.errorCount > 0
                ? Math.round((endpoint.successCount / (endpoint.successCount + endpoint.errorCount)) * 100)
                : 0;

            console.log(`   ${status} ${endpoint.name}: ${successRate}% успех, ${endpoint.averageResponseTime}ms`);
        });
    }
}

// Создаем глобальный экземпляр
const globalRPCManager = new CentralizedRPCManager();

module.exports = {
    CentralizedRPCManager,
    globalRPCManager
};
