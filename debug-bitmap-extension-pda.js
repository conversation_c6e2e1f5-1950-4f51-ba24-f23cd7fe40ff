/**
 * 🔍 ДИАГНОСТИКА BITMAP EXTENSION PDA
 * Проверяем что генерирует наша функция vs правильные PDA
 */

const { PublicKey } = require('@solana/web3.js');

console.log('🔍 ДИАГНОСТИКА BITMAP EXTENSION PDA\n');

function debugBitmapExtensionPDA() {
    try {
        console.log('📊 СРАВНЕНИЕ НАШЕЙ ГЕНЕРАЦИИ С ПРАВИЛЬНЫМИ PDA');
        console.log('==========================================');

        const METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        const WSOL_USDC_POOL = new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6');

        // Правильные PDA из SDK перехвата
        const CORRECT_PDAS = {
            binId_minus8: 'GHhTdx41vXRhzUGxCAUhpYxsi2P5668JYW7R38fuxkDQ',
            binId_minus9: 'FNJWja8xKbR3CQH9aF8kv4rix6YXriqunDAyS3E2GG9c'
        };

        // Наш проблемный PDA
        const OUR_PROBLEMATIC_PDA = '4Kq7Q1MrfgTTL2CwUKjHvJyFmuG8Pa6CJgDBom5QkUSH';

        console.log(`🎯 Наш проблемный PDA: ${OUR_PROBLEMATIC_PDA}`);
        console.log(`✅ Правильный PDA (binId: -8): ${CORRECT_PDAS.binId_minus8}`);
        console.log(`✅ Правильный PDA (binId: -9): ${CORRECT_PDAS.binId_minus9}`);

        // Функция создания bin ID буфера
        function createBinIdBuffer(binId) {
            const buffer = Buffer.alloc(8);
            buffer.writeBigInt64LE(BigInt(binId), 0);
            return buffer;
        }

        // Функция генерации Bitmap Extension (наша)
        function getBinArrayBitmapExtension(lbPairAddress, binId = 0) {
            const seeds = [
                Buffer.from("bin_array"),
                lbPairAddress.toBuffer(),
                createBinIdBuffer(binId)
            ];

            const [pda, bump] = PublicKey.findProgramAddressSync(seeds, METEORA_DLMM_PROGRAM);
            return { pda, bump };
        }

        console.log('\n📊 ТЕСТИРОВАНИЕ РАЗЛИЧНЫХ BIN ID:');
        console.log('==========================================');

        const binIdsToTest = [
            0, -1, 1, -8, -9, -4517, -4518, -4516,
            -69, -70, -68, -485, -486, -484
        ];

        let foundMatches = [];

        binIdsToTest.forEach(binId => {
            try {
                const result = getBinArrayBitmapExtension(WSOL_USDC_POOL, binId);
                const pda = result.pda.toString();
                
                console.log(`   Bin ID ${binId.toString().padStart(6)}: ${pda}`);
                
                // Проверяем совпадения с правильными PDA
                if (pda === CORRECT_PDAS.binId_minus8) {
                    console.log(`      🎉 СОВПАДЕНИЕ с правильным PDA (binId: -8)!`);
                    foundMatches.push({ binId, pda, type: 'binId_minus8' });
                }
                if (pda === CORRECT_PDAS.binId_minus9) {
                    console.log(`      🎉 СОВПАДЕНИЕ с правильным PDA (binId: -9)!`);
                    foundMatches.push({ binId, pda, type: 'binId_minus9' });
                }
                if (pda === OUR_PROBLEMATIC_PDA) {
                    console.log(`      ❌ СОВПАДЕНИЕ с нашим проблемным PDA!`);
                    foundMatches.push({ binId, pda, type: 'problematic' });
                }
            } catch (error) {
                console.log(`   Bin ID ${binId.toString().padStart(6)}: ❌ ОШИБКА - ${error.message}`);
            }
        });

        console.log('\n📊 РЕЗУЛЬТАТЫ ПОИСКА:');
        console.log('==========================================');

        if (foundMatches.length > 0) {
            console.log(`🎉 НАЙДЕНО ${foundMatches.length} СОВПАДЕНИЙ:`);
            foundMatches.forEach((match, index) => {
                console.log(`   ${index + 1}. Bin ID: ${match.binId}, Тип: ${match.type}`);
                console.log(`      PDA: ${match.pda}`);
            });
        } else {
            console.log(`❌ НЕ НАЙДЕНО СОВПАДЕНИЙ!`);
            console.log(`   Возможно нужно тестировать другие bin ID или другую логику`);
        }

        console.log('\n🔍 ДОПОЛНИТЕЛЬНАЯ ДИАГНОСТИКА:');
        console.log('==========================================');

        // Проверяем что происходит с активным bin ID из логов
        const ACTIVE_BIN_ID_FROM_LOGS = -4517;
        console.log(`📊 Тестируем активный bin ID из логов: ${ACTIVE_BIN_ID_FROM_LOGS}`);
        
        const activeResult = getBinArrayBitmapExtension(WSOL_USDC_POOL, ACTIVE_BIN_ID_FROM_LOGS);
        console.log(`   Результат: ${activeResult.pda.toString()}`);
        console.log(`   Bump: ${activeResult.bump}`);
        
        if (activeResult.pda.toString() === OUR_PROBLEMATIC_PDA) {
            console.log(`   🎯 ЭТО НАШ ПРОБЛЕМНЫЙ PDA! Bin ID: ${ACTIVE_BIN_ID_FROM_LOGS}`);
        }

        console.log('\n🔧 ПРОВЕРКА OWNERSHIP:');
        console.log('==========================================');
        console.log(`📋 Для проверки ownership выполните:`);
        console.log(`   solana account ${OUR_PROBLEMATIC_PDA}`);
        console.log(`   solana account ${CORRECT_PDAS.binId_minus8}`);
        console.log(`   solana account ${CORRECT_PDAS.binId_minus9}`);

        console.log('\n🎯 РЕКОМЕНДАЦИИ:');
        console.log('==========================================');
        
        const correctMatches = foundMatches.filter(m => m.type.includes('binId'));
        if (correctMatches.length > 0) {
            console.log(`✅ ИСПОЛЬЗУЙТЕ ПРАВИЛЬНЫЙ BIN ID:`);
            correctMatches.forEach(match => {
                console.log(`   Bin ID: ${match.binId} → PDA: ${match.pda}`);
            });
        } else {
            console.log(`❌ НЕ НАЙДЕН ПРАВИЛЬНЫЙ BIN ID`);
            console.log(`🔧 РЕКОМЕНДАЦИИ:`);
            console.log(`   1. Проверить что активный bin ID берется из правильного источника`);
            console.log(`   2. Использовать статические адреса как fallback`);
            console.log(`   3. Проверить логику генерации bin ID в кэше`);
        }

        return foundMatches;

    } catch (error) {
        console.error(`❌ КРИТИЧЕСКАЯ ОШИБКА В ДИАГНОСТИКЕ:`, error.message);
        console.error(`📋 Stack trace:`, error.stack);
        return [];
    }
}

// Запуск диагностики
const matches = debugBitmapExtensionPDA();

if (matches.length > 0) {
    console.log(`\n🎉 ДИАГНОСТИКА ЗАВЕРШЕНА! Найдено ${matches.length} совпадений.`);
    process.exit(0);
} else {
    console.log(`\n❌ ДИАГНОСТИКА НЕ ДАЛА РЕЗУЛЬТАТОВ!`);
    process.exit(1);
}
