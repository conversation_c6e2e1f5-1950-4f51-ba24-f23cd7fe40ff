const { PublicKey, TransactionInstruction } = require('@solana/web3.js');
const { createAssociatedTokenAccountIdempotentInstruction, getAssociatedTokenAddress } = require('@solana/spl-token');

/**
 * 🔥 ИЗМЕРЕНИЕ ВЕСА ВСЕХ ИНСТРУКЦИЙ FLASH LOAN АРБИТРАЖА
 */
class InstructionWeightMeasurer {
    constructor() {
        // Программы
        this.MARGINFI_PROGRAM = new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA');
        this.METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        this.TOKEN_PROGRAM = new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA');
        this.SYSTEM_PROGRAM = new PublicKey('********************************');
        
        // Тестовые адреса
        this.userWallet = new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV');
        this.marginfiAccount = new PublicKey('8KGKCCb6KhqGYKKCQcbPVp8YdKKKKKKKKKKKKKKKKKKK'); // Тестовый
        this.poolAddress1 = new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6');
        this.poolAddress2 = new PublicKey('********************************************');
        
        // Mints
        this.WSOL_MINT = new PublicKey('So********************************111111112');
        this.USDC_MINT = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');
        
        // Banks (тестовые)
        this.USDC_BANK = new PublicKey('CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh');
        this.SOL_BANK = new PublicKey('CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh');
        
        // ALT таблицы для сжатия
        this.altMap = new Map();
        this.loadALTTables();
    }

    /**
     * 🔥 ЗАГРУЗКА ALT ТАБЛИЦ
     */
    loadALTTables() {
        try {
            const fs = require('fs');
            const altData = JSON.parse(fs.readFileSync('custom-alt-data.json', 'utf8'));
            
            // MarginFi ALT
            if (altData.tables.marginfi1) {
                altData.tables.marginfi1.addresses.forEach((address, index) => {
                    this.altMap.set(address, { tableIndex: 0, addressIndex: index, tableName: 'MarginFi' });
                });
            }

            // Custom ALT
            if (altData.tables.custom) {
                altData.tables.custom.addresses.forEach((address, index) => {
                    this.altMap.set(address, { tableIndex: 1, addressIndex: index, tableName: 'Custom' });
                });
            }

            console.log(`✅ ALT таблицы загружены: ${this.altMap.size} адресов`);
        } catch (error) {
            console.log(`⚠️ ALT таблицы не загружены: ${error.message}`);
        }
    }

    /**
     * 🔥 РАСЧЕТ ВЕСА ИНСТРУКЦИИ С ALT СЖАТИЕМ
     */
    calculateInstructionWeight(instruction, name) {
        // Вес без сжатия
        const programIdBytes = 32;
        const dataBytes = instruction.data.length;
        const accountBytes = instruction.keys.length * 33; // 32 байта pubkey + 1 байт флаги
        const metadataBytes = 16;
        const uncompressedBytes = programIdBytes + dataBytes + accountBytes + metadataBytes;

        // ALT сжатие
        let compressibleCount = 0;
        instruction.keys.forEach(key => {
            const addressStr = key.pubkey.toString();
            if (this.altMap.has(addressStr) && !key.isSigner) {
                compressibleCount++;
            }
        });

        const altSavings = compressibleCount * 31; // 32 байта → 1 байт
        const altOverhead = 16; // 2 ALT таблицы × 8 байт
        const compressedBytes = uncompressedBytes - altSavings + altOverhead;

        // Compute Units
        const baseWeight = this.getBaseWeight(name);
        const accountWeight = instruction.keys.length * 100;
        const writableWeight = instruction.keys.filter(k => k.isWritable).length * 200;
        const signerWeight = instruction.keys.filter(k => k.isSigner).length * 300;
        const dataWeight = instruction.data.length * 10;
        const computeUnits = baseWeight + accountWeight + writableWeight + signerWeight + dataWeight;

        return {
            name,
            accounts: instruction.keys.length,
            dataBytes,
            uncompressedBytes,
            compressedBytes,
            savings: uncompressedBytes - compressedBytes,
            computeUnits,
            compressibleAccounts: compressibleCount
        };
    }

    /**
     * 🔥 БАЗОВЫЙ ВЕС ПО ТИПУ ИНСТРУКЦИИ
     */
    getBaseWeight(name) {
        const weights = {
            'START Flash Loan': 5000,
            'END Flash Loan': 5000,
            'CREATE ATA': 3000,
            'BORROW': 8000,
            'REPAY': 8000,
            'ADD Liquidity': 15000,
            'REMOVE Liquidity': 12000,
            'SWAP': 10000,
            'CLAIM Fee': 8000
        };

        for (const [key, weight] of Object.entries(weights)) {
            if (name.includes(key)) return weight;
        }
        return 5000; // Дефолтный
    }

    /**
     * 🔥 1. START FLASH LOAN
     */
    createStartFlashLoanInstruction() {
        const discriminator = Buffer.from([14, 131, 33, 220, 81, 186, 180, 107]);
        const endIndex = Buffer.alloc(8);
        endIndex.writeBigUInt64LE(BigInt(13), 0); // 13 инструкций до END
        
        const data = Buffer.concat([discriminator, endIndex]);
        const INSTRUCTIONS_SYSVAR = new PublicKey('Sysvar1nstructions1111111111111111111111111');

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: [
                { pubkey: this.marginfiAccount, isSigner: false, isWritable: true },
                { pubkey: this.userWallet, isSigner: true, isWritable: true },
                { pubkey: INSTRUCTIONS_SYSVAR, isSigner: false, isWritable: false }
            ],
            data
        });
    }

    /**
     * 🔥 2. CREATE USDC ATA
     */
    async createUSDCATAInstruction() {
        const usdcATA = await getAssociatedTokenAddress(this.USDC_MINT, this.userWallet);
        
        return createAssociatedTokenAccountIdempotentInstruction(
            this.userWallet,  // payer
            usdcATA,          // ata
            this.userWallet,  // owner
            this.USDC_MINT    // mint
        );
    }

    /**
     * 🔥 3. CREATE SOL ATA
     */
    async createSOLATAInstruction() {
        const solATA = await getAssociatedTokenAddress(this.WSOL_MINT, this.userWallet);
        
        return createAssociatedTokenAccountIdempotentInstruction(
            this.userWallet,  // payer
            solATA,           // ata
            this.userWallet,  // owner
            this.WSOL_MINT    // mint
        );
    }

    /**
     * 🔥 4. BORROW USDC
     */
    createBorrowUSDCInstruction() {
        const discriminator = Buffer.from([228, 253, 131, 202, 207, 116, 89, 18]);
        const amount = Buffer.alloc(8);
        amount.writeBigUInt64LE(BigInt(**********), 0); // 1000 USDC
        
        const data = Buffer.concat([discriminator, amount]);

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: [
                { pubkey: this.marginfiAccount, isSigner: false, isWritable: true },
                { pubkey: this.userWallet, isSigner: true, isWritable: false },
                { pubkey: this.USDC_BANK, isSigner: false, isWritable: true },
                { pubkey: new PublicKey('********************************'), isSigner: false, isWritable: false }, // destination_token_account
                { pubkey: new PublicKey('22222222222222222222222222222222'), isSigner: false, isWritable: true }, // bank_liquidity_vault_authority
                { pubkey: new PublicKey('********************************'), isSigner: false, isWritable: true }, // bank_liquidity_vault
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false }
            ],
            data
        });
    }

    /**
     * 🔥 5. BORROW WSOL
     */
    createBorrowWSOLInstruction() {
        const discriminator = Buffer.from([228, 253, 131, 202, 207, 116, 89, 18]);
        const amount = Buffer.alloc(8);
        amount.writeBigUInt64LE(BigInt(**********), 0); // 5 WSOL
        
        const data = Buffer.concat([discriminator, amount]);

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: [
                { pubkey: this.marginfiAccount, isSigner: false, isWritable: true },
                { pubkey: this.userWallet, isSigner: true, isWritable: false },
                { pubkey: this.SOL_BANK, isSigner: false, isWritable: true },
                { pubkey: new PublicKey('********************************'), isSigner: false, isWritable: false },
                { pubkey: new PublicKey('22222222222222222222222222222222'), isSigner: false, isWritable: true },
                { pubkey: new PublicKey('********************************'), isSigner: false, isWritable: true },
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false }
            ],
            data
        });
    }

    /**
     * 🔥 ОСНОВНОЙ МЕТОД ИЗМЕРЕНИЯ
     */
    async measureAllInstructions() {
        console.log('🔥 ИЗМЕРЕНИЕ ВЕСА ВСЕХ ИНСТРУКЦИЙ FLASH LOAN АРБИТРАЖА\n');

        const instructions = [];
        
        // 1. START Flash Loan
        instructions.push({
            instruction: this.createStartFlashLoanInstruction(),
            name: 'START Flash Loan'
        });

        // 2-3. CREATE ATA
        instructions.push({
            instruction: await this.createUSDCATAInstruction(),
            name: 'CREATE USDC ATA'
        });
        
        instructions.push({
            instruction: await this.createSOLATAInstruction(),
            name: 'CREATE SOL ATA'
        });

        // 4-5. BORROW
        instructions.push({
            instruction: this.createBorrowUSDCInstruction(),
            name: 'BORROW USDC'
        });
        
        instructions.push({
            instruction: this.createBorrowWSOLInstruction(),
            name: 'BORROW WSOL'
        });

        // Измеряем все инструкции
        const results = [];
        let totalUncompressed = 0;
        let totalCompressed = 0;
        let totalComputeUnits = 0;

        console.log('📊 ДЕТАЛЬНЫЙ АНАЛИЗ КАЖДОЙ ИНСТРУКЦИИ:\n');

        instructions.forEach((item, index) => {
            const weight = this.calculateInstructionWeight(item.instruction, item.name);
            results.push(weight);
            
            totalUncompressed += weight.uncompressedBytes;
            totalCompressed += weight.compressedBytes;
            totalComputeUnits += weight.computeUnits;

            console.log(`${index + 1}. ${weight.name}:`);
            console.log(`   📦 Аккаунты: ${weight.accounts}`);
            console.log(`   📄 Данные: ${weight.dataBytes} байт`);
            console.log(`   📊 Без сжатия: ${weight.uncompressedBytes} байт`);
            console.log(`   🗜️ С ALT сжатием: ${weight.compressedBytes} байт`);
            console.log(`   💾 Экономия: ${weight.savings} байт`);
            console.log(`   ⚡ Compute Units: ${weight.computeUnits} CU`);
            console.log(`   🔗 ALT сжимаемые: ${weight.compressibleAccounts}/${weight.accounts}\n`);
        });

        // Итоговая статистика
        console.log('🎯 ИТОГОВАЯ СТАТИСТИКА:\n');
        console.log(`📊 Всего инструкций: ${instructions.length}`);
        console.log(`📦 Общий вес без сжатия: ${totalUncompressed} байт`);
        console.log(`🗜️ Общий вес с ALT сжатием: ${totalCompressed} байт`);
        console.log(`💾 Общая экономия: ${totalUncompressed - totalCompressed} байт (${Math.round((totalUncompressed - totalCompressed) / totalUncompressed * 100)}%)`);
        console.log(`⚡ Общие Compute Units: ${totalComputeUnits} CU`);
        console.log(`🚨 Лимит транзакции: 1232 байт`);
        console.log(`📈 Использование: ${Math.round(totalCompressed / 1232 * 100)}% от лимита`);
        console.log(`🔥 Остается места: ${1232 - totalCompressed} байт\n`);

        return results;
    }
}

// Запуск измерения
async function main() {
    const measurer = new InstructionWeightMeasurer();
    await measurer.measureAllInstructions();
}

main().catch(console.error);
