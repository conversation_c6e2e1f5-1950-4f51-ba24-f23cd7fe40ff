/**
 * 🔧 ПОЛНЫЙ СКАНЕР PDA ДЛЯ WSOL-USDC
 * Генерирует, проверяет и экспортирует все PDA для WSOL-USDC пула
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const BN = require('bn.js');
const fs = require('fs');
const path = require('path');

console.log('🔧 ПОЛНЫЙ СКАНЕР PDA ДЛЯ WSOL-USDC\n');

class WSOLUSDCPDAScanner {
    constructor() {
        this.METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        
        // 🎯 WSOL-USDC СПЕЦИФИЧНЫЕ КОНСТАНТЫ
        this.WSOL_USDC_LB_PAIR = new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6'); // НАШ ОСНОВНОЙ ПУЛ!
        this.WSOL_MINT = new PublicKey('So11111111111111111111111111111111111111112');
        this.USDC_MINT = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');
        
        // НАШ ПОЛЬЗОВАТЕЛЬ
        this.USER_PUBLIC_KEY = new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV');
        
        this.connection = new Connection('https://api.mainnet-beta.solana.com');
    }

    /**
     * 🎯 КАНОНИЧНЫЕ ГЕНЕРАТОРЫ PDA ДЛЯ WSOL-USDC
     */
    
    getPositionPDA(user) {
        return PublicKey.findProgramAddressSync(
            [Buffer.from("position"), this.WSOL_USDC_LB_PAIR.toBuffer(), user.toBuffer()],
            this.METEORA_DLMM_PROGRAM
        );
    }

    getBinArrayBitmapExtensionPDA(binId) {
        // 🔧 КАНОНИЧНОЕ КОДИРОВАНИЕ: BN.js с toTwos для signed i64 LE
        const binIdBuffer = new BN(binId).toTwos(64).toArrayLike(Buffer, 'le', 8);
        return PublicKey.findProgramAddressSync(
            [Buffer.from("bin_array"), this.WSOL_USDC_LB_PAIR.toBuffer(), binIdBuffer],
            this.METEORA_DLMM_PROGRAM
        );
    }

    getEventAuthorityPDA() {
        return PublicKey.findProgramAddressSync(
            [Buffer.from("__event_authority")],
            this.METEORA_DLMM_PROGRAM
        );
    }

    getStrategyPDA(user) {
        return PublicKey.findProgramAddressSync(
            [Buffer.from("strategy"), this.WSOL_USDC_LB_PAIR.toBuffer(), user.toBuffer()],
            this.METEORA_DLMM_PROGRAM
        );
    }

    getPoolReservePDA(mint) {
        return PublicKey.findProgramAddressSync(
            [Buffer.from("reserve"), this.WSOL_USDC_LB_PAIR.toBuffer(), mint.toBuffer()],
            this.METEORA_DLMM_PROGRAM
        );
    }

    getTreasuryPDA() {
        return PublicKey.findProgramAddressSync(
            [Buffer.from("treasury"), this.WSOL_USDC_LB_PAIR.toBuffer()],
            this.METEORA_DLMM_PROGRAM
        );
    }

    getFeeVaultPDA(mint) {
        return PublicKey.findProgramAddressSync(
            [Buffer.from("fee_vault"), this.WSOL_USDC_LB_PAIR.toBuffer(), mint.toBuffer()],
            this.METEORA_DLMM_PROGRAM
        );
    }

    /**
     * 🔍 ПРОВЕРКА СУЩЕСТВОВАНИЯ АККАУНТА
     */
    async accountExists(address) {
        try {
            const accountInfo = await this.connection.getAccountInfo(address);
            return {
                exists: accountInfo !== null,
                owner: accountInfo ? accountInfo.owner.toString() : null,
                dataLength: accountInfo ? accountInfo.data.length : 0,
                lamports: accountInfo ? accountInfo.lamports : 0
            };
        } catch (error) {
            return { exists: false, error: error.message };
        }
    }

    /**
     * 🔍 БРУТФОРС BITMAP EXTENSION PDA
     */
    async findValidBitmapExtension(startRange = -2000, endRange = 2000) {
        console.log(`🔍 Поиск валидного Bitmap Extension PDA (диапазон: ${startRange} до ${endRange})`);
        console.log('------------------------------------------');

        for (let binId = startRange; binId <= endRange; binId++) {
            try {
                const [bitmapPDA, bump] = this.getBinArrayBitmapExtensionPDA(binId);
                const result = await this.accountExists(bitmapPDA);
                
                if (result.exists) {
                    console.log(`🎉 НАЙДЕН ВАЛИДНЫЙ BITMAP EXTENSION!`);
                    console.log(`   Bin ID: ${binId}`);
                    console.log(`   PDA: ${bitmapPDA.toString()}`);
                    console.log(`   Bump: ${bump}`);
                    console.log(`   Owner: ${result.owner}`);
                    console.log(`   Data Length: ${result.dataLength} байт`);
                    
                    return { binId, pda: bitmapPDA, bump, ...result };
                }

                // Логируем прогресс каждые 200 итераций
                if (binId % 200 === 0) {
                    console.log(`   Проверено до bin ID: ${binId}...`);
                }

            } catch (error) {
                // Игнорируем ошибки генерации
            }
        }

        console.log(`❌ Валидный Bitmap Extension не найден в диапазоне ${startRange} - ${endRange}`);
        return null;
    }

    /**
     * 🚀 ПОЛНОЕ СКАНИРОВАНИЕ ВСЕХ PDA
     */
    async scanAllPDAs() {
        console.log('🚀 ПОЛНОЕ СКАНИРОВАНИЕ PDA ДЛЯ WSOL-USDC');
        console.log('==========================================');
        console.log(`📊 LB Pair: ${this.WSOL_USDC_LB_PAIR.toString()}`);
        console.log(`👤 User: ${this.USER_PUBLIC_KEY.toString()}`);
        console.log(`🪙 WSOL Mint: ${this.WSOL_MINT.toString()}`);
        console.log(`🪙 USDC Mint: ${this.USDC_MINT.toString()}`);

        // Генерируем все PDA
        const [positionPDA, positionBump] = this.getPositionPDA(this.USER_PUBLIC_KEY);
        const [eventAuthorityPDA, eventAuthorityBump] = this.getEventAuthorityPDA();
        const [strategyPDA, strategyBump] = this.getStrategyPDA(this.USER_PUBLIC_KEY);
        const [reserveXPDA, reserveXBump] = this.getPoolReservePDA(this.WSOL_MINT);
        const [reserveYPDA, reserveYBump] = this.getPoolReservePDA(this.USDC_MINT);
        const [treasuryPDA, treasuryBump] = this.getTreasuryPDA();
        const [feeVaultXPDA, feeVaultXBump] = this.getFeeVaultPDA(this.WSOL_MINT);
        const [feeVaultYPDA, feeVaultYBump] = this.getFeeVaultPDA(this.USDC_MINT);

        console.log('\n📋 СГЕНЕРИРОВАННЫЕ PDA:');
        console.log('------------------------------------------');
        console.log(`Position PDA:        ${positionPDA.toString()} (bump: ${positionBump})`);
        console.log(`Event Authority:     ${eventAuthorityPDA.toString()} (bump: ${eventAuthorityBump})`);
        console.log(`Strategy PDA:        ${strategyPDA.toString()} (bump: ${strategyBump})`);
        console.log(`Reserve X (WSOL):    ${reserveXPDA.toString()} (bump: ${reserveXBump})`);
        console.log(`Reserve Y (USDC):    ${reserveYPDA.toString()} (bump: ${reserveYBump})`);
        console.log(`Treasury:            ${treasuryPDA.toString()} (bump: ${treasuryBump})`);
        console.log(`Fee Vault X (WSOL):  ${feeVaultXPDA.toString()} (bump: ${feeVaultXBump})`);
        console.log(`Fee Vault Y (USDC):  ${feeVaultYPDA.toString()} (bump: ${feeVaultYBump})`);

        // Проверяем существование через RPC
        console.log('\n🔍 ПРОВЕРКА СУЩЕСТВОВАНИЯ ЧЕРЕЗ RPC:');
        console.log('------------------------------------------');

        const checkAndPrint = async (label, pda) => {
            const result = await this.accountExists(pda);
            const status = result.exists ? '✅ Существует' : '❌ Не найден';
            const owner = result.exists ? ` (Owner: ${result.owner.slice(0,8)}...)` : '';
            console.log(`${label}: ${status}${owner}`);
            return result;
        };

        const results = {
            position: await checkAndPrint('Position PDA        ', positionPDA),
            eventAuthority: await checkAndPrint('Event Authority     ', eventAuthorityPDA),
            strategy: await checkAndPrint('Strategy PDA        ', strategyPDA),
            reserveX: await checkAndPrint('Reserve X (WSOL)    ', reserveXPDA),
            reserveY: await checkAndPrint('Reserve Y (USDC)    ', reserveYPDA),
            treasury: await checkAndPrint('Treasury            ', treasuryPDA),
            feeVaultX: await checkAndPrint('Fee Vault X (WSOL)  ', feeVaultXPDA),
            feeVaultY: await checkAndPrint('Fee Vault Y (USDC)  ', feeVaultYPDA)
        };

        // Поиск валидного Bitmap Extension
        const bitmapResult = await this.findValidBitmapExtension();

        // Собираем все данные
        const pdaData = {
            lbPair: this.WSOL_USDC_LB_PAIR.toString(),
            user: this.USER_PUBLIC_KEY.toString(),
            pdas: {
                position: { address: positionPDA.toString(), bump: positionBump, exists: results.position.exists },
                eventAuthority: { address: eventAuthorityPDA.toString(), bump: eventAuthorityBump, exists: results.eventAuthority.exists },
                strategy: { address: strategyPDA.toString(), bump: strategyBump, exists: results.strategy.exists },
                reserveX: { address: reserveXPDA.toString(), bump: reserveXBump, exists: results.reserveX.exists },
                reserveY: { address: reserveYPDA.toString(), bump: reserveYBump, exists: results.reserveY.exists },
                treasury: { address: treasuryPDA.toString(), bump: treasuryBump, exists: results.treasury.exists },
                feeVaultX: { address: feeVaultXPDA.toString(), bump: feeVaultXBump, exists: results.feeVaultX.exists },
                feeVaultY: { address: feeVaultYPDA.toString(), bump: feeVaultYBump, exists: results.feeVaultY.exists },
                bitmapExtension: bitmapResult ? {
                    address: bitmapResult.pda.toString(),
                    bump: bitmapResult.bump,
                    binId: bitmapResult.binId,
                    exists: true
                } : null
            },
            timestamp: new Date().toISOString()
        };

        return pdaData;
    }

    /**
     * 📁 ЭКСПОРТ РЕЗУЛЬТАТОВ
     */
    async exportResults(pdaData) {
        console.log('\n📁 ЭКСПОРТ РЕЗУЛЬТАТОВ:');
        console.log('------------------------------------------');

        // Создаем директорию out
        const outDir = path.join(__dirname, 'out');
        if (!fs.existsSync(outDir)) {
            fs.mkdirSync(outDir, { recursive: true });
        }

        // 1. JSON файл
        const jsonPath = path.join(outDir, 'wsol-usdc-pda-map.json');
        fs.writeFileSync(jsonPath, JSON.stringify(pdaData, null, 2));
        console.log(`✅ JSON экспорт: ${jsonPath}`);

        // 2. JavaScript константы
        const jsContent = `/**
 * 🎯 WSOL-USDC PDA КОНСТАНТЫ (АВТОГЕНЕРАЦИЯ)
 * Сгенерировано: ${pdaData.timestamp}
 */

const { PublicKey } = require('@solana/web3.js');

// 🔧 ОСНОВНЫЕ КОНСТАНТЫ
const WSOL_USDC_LB_PAIR = new PublicKey('${pdaData.lbPair}');
const USER_PUBLIC_KEY = new PublicKey('${pdaData.user}');

// 🎯 PDA АДРЕСА
const WSOL_USDC_PDA = {
    POSITION: new PublicKey('${pdaData.pdas.position.address}'),
    EVENT_AUTHORITY: new PublicKey('${pdaData.pdas.eventAuthority.address}'),
    STRATEGY: new PublicKey('${pdaData.pdas.strategy.address}'),
    RESERVE_X: new PublicKey('${pdaData.pdas.reserveX.address}'),
    RESERVE_Y: new PublicKey('${pdaData.pdas.reserveY.address}'),
    TREASURY: new PublicKey('${pdaData.pdas.treasury.address}'),
    FEE_VAULT_X: new PublicKey('${pdaData.pdas.feeVaultX.address}'),
    FEE_VAULT_Y: new PublicKey('${pdaData.pdas.feeVaultY.address}'),${pdaData.pdas.bitmapExtension ? `
    BITMAP_EXTENSION: new PublicKey('${pdaData.pdas.bitmapExtension.address}'),
    BITMAP_BIN_ID: ${pdaData.pdas.bitmapExtension.binId},` : ''}
};

module.exports = {
    WSOL_USDC_LB_PAIR,
    USER_PUBLIC_KEY,
    WSOL_USDC_PDA
};
`;

        const jsPath = path.join(outDir, 'wsol-usdc-pda-constants.js');
        fs.writeFileSync(jsPath, jsContent);
        console.log(`✅ JavaScript константы: ${jsPath}`);

        // 3. .env файл
        const envContent = `# WSOL-USDC PDA АДРЕСА (АВТОГЕНЕРАЦИЯ)
# Сгенерировано: ${pdaData.timestamp}

PDA_LB_PAIR=${pdaData.lbPair}
PDA_USER=${pdaData.user}
PDA_POSITION=${pdaData.pdas.position.address}
PDA_EVENT_AUTHORITY=${pdaData.pdas.eventAuthority.address}
PDA_STRATEGY=${pdaData.pdas.strategy.address}
PDA_RESERVE_X=${pdaData.pdas.reserveX.address}
PDA_RESERVE_Y=${pdaData.pdas.reserveY.address}
PDA_TREASURY=${pdaData.pdas.treasury.address}
PDA_FEE_VAULT_X=${pdaData.pdas.feeVaultX.address}
PDA_FEE_VAULT_Y=${pdaData.pdas.feeVaultY.address}${pdaData.pdas.bitmapExtension ? `
PDA_BITMAP_EXTENSION=${pdaData.pdas.bitmapExtension.address}
PDA_BITMAP_BIN_ID=${pdaData.pdas.bitmapExtension.binId}` : ''}
`;

        const envPath = path.join(outDir, '.env.wsol-usdc-pda');
        fs.writeFileSync(envPath, envContent);
        console.log(`✅ Environment файл: ${envPath}`);

        console.log('\n🎉 ЭКСПОРТ ЗАВЕРШЕН!');
        return { jsonPath, jsPath, envPath };
    }
}

// Запуск сканирования
async function main() {
    try {
        const scanner = new WSOLUSDCPDAScanner();
        const pdaData = await scanner.scanAllPDAs();
        const exportPaths = await scanner.exportResults(pdaData);

        console.log('\n🎯 ИТОГОВАЯ СВОДКА:');
        console.log('==========================================');
        
        const existingPDAs = Object.values(pdaData.pdas).filter(pda => pda && pda.exists).length;
        const totalPDAs = Object.values(pdaData.pdas).filter(pda => pda !== null).length;
        
        console.log(`📊 Существующих PDA: ${existingPDAs}/${totalPDAs}`);
        console.log(`📁 Файлы экспорта: 3 файла созданы`);
        console.log(`✅ Готово к интеграции в основной код`);

        if (pdaData.pdas.bitmapExtension) {
            console.log(`\n🎉 НАЙДЕН BITMAP EXTENSION!`);
            console.log(`   Bin ID: ${pdaData.pdas.bitmapExtension.binId}`);
            console.log(`   PDA: ${pdaData.pdas.bitmapExtension.address}`);
            console.log(`   ✅ Ошибка 3007 должна быть исправлена!`);
        }

        process.exit(0);

    } catch (error) {
        console.error(`❌ КРИТИЧЕСКАЯ ОШИБКА:`, error.message);
        console.error(`📋 Stack trace:`, error.stack);
        process.exit(1);
    }
}

// Запуск если файл вызван напрямую
if (require.main === module) {
    main();
}

module.exports = WSOLUSDCPDAScanner;
