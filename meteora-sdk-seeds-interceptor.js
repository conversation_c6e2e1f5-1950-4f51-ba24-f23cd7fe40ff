/**
 * 🔍 METEORA SDK SEEDS INTERCEPTOR
 * Создаем инструкцию через SDK и перехватываем все seeds для PDA
 */

const { PublicKey, Keypair, Connection } = require('@solana/web3.js');
const DLMM = require('@meteora-ag/dlmm').default;
const BN = require('bn.js');

console.log('🔍 METEORA SDK SEEDS INTERCEPTOR\n');

class MeteoraSDKSeedsInterceptor {
    constructor() {
        this.connection = new Connection('https://api.mainnet-beta.solana.com');
        
        // Данные из успешной транзакции
        this.SUCCESS_DATA = {
            lbPair: 'E9kQAzGFvgC7xtgNGCe63meEKBFw2T5yNjzgbJAtNcSJ', // Kai-WSOL
            user: 'H6uwL8TyV54xAUnarAwMLnRQtEpAdRDzoEjkfFbyF8vS',
            position: '9ZxeGGgXYsFykPhBkP5tizx5WDWB5QQJjzyahRwwTGxb',
            binArrayBitmapExtension: 'GHhTdx41vXRhzUGxCAUhpYxsi2P5668JYW7R38fuxkDQ',
            activeBinId: -485,
            amountX: 43297240969, // Kai
            amountY: 100000000     // WSOL (0.1)
        };
        
        // Наши данные
        this.OUR_DATA = {
            lbPair: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // WSOL-USDC
            user: 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV',
            position: 'Gbv33r6KGduHGWb8D5m4jhJMTqnGE4oaMun7ToHLr1UC',
            binArrayBitmapExtension: '59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li'
        };
        
        this.interceptedSeeds = [];
    }

    /**
     * 🔧 ПЕРЕХВАТЧИК PublicKey.findProgramAddressSync
     */
    setupSeedsInterceptor() {
        console.log('🔧 НАСТРОЙКА ПЕРЕХВАТЧИКА SEEDS...');
        
        // Сохраняем оригинальную функцию
        const originalFindProgramAddressSync = PublicKey.findProgramAddressSync;
        const originalFindProgramAddress = PublicKey.findProgramAddress;
        
        // Перехватываем синхронную версию
        PublicKey.findProgramAddressSync = (seeds, programId) => {
            const result = originalFindProgramAddressSync.call(PublicKey, seeds, programId);
            
            // Логируем перехваченные seeds
            const seedsInfo = {
                seeds: seeds.map(seed => ({
                    type: Buffer.isBuffer(seed) ? 'Buffer' : typeof seed,
                    value: Buffer.isBuffer(seed) ? seed.toString('hex') : seed.toString(),
                    length: Buffer.isBuffer(seed) ? seed.length : 0
                })),
                programId: programId.toString(),
                resultPDA: result[0].toString(),
                bump: result[1],
                timestamp: Date.now()
            };
            
            this.interceptedSeeds.push(seedsInfo);
            
            console.log(`🔍 ПЕРЕХВАЧЕН PDA:`);
            console.log(`   Program ID: ${programId.toString().slice(0,8)}...`);
            console.log(`   Result PDA: ${result[0].toString()}`);
            console.log(`   Bump: ${result[1]}`);
            console.log(`   Seeds (${seeds.length}):`);
            seeds.forEach((seed, index) => {
                if (Buffer.isBuffer(seed)) {
                    console.log(`      [${index}] Buffer(${seed.length}): ${seed.toString('hex')}`);
                    // Пробуем интерпретировать как строку
                    try {
                        const asString = seed.toString('utf8');
                        if (/^[a-zA-Z_]+$/.test(asString)) {
                            console.log(`          Как строка: "${asString}"`);
                        }
                    } catch (e) {}
                    // Пробуем интерпретировать как число
                    if (seed.length === 4) {
                        const asInt32LE = seed.readInt32LE(0);
                        const asUInt32LE = seed.readUInt32LE(0);
                        console.log(`          Как int32LE: ${asInt32LE}`);
                        console.log(`          Как uint32LE: ${asUInt32LE}`);
                    }
                } else {
                    console.log(`      [${index}] ${typeof seed}: ${seed.toString()}`);
                }
            });
            console.log('');
            
            return result;
        };
        
        // Перехватываем асинхронную версию
        PublicKey.findProgramAddress = async (seeds, programId) => {
            const result = await originalFindProgramAddress.call(PublicKey, seeds, programId);
            
            // Аналогичное логирование для async версии
            const seedsInfo = {
                seeds: seeds.map(seed => ({
                    type: Buffer.isBuffer(seed) ? 'Buffer' : typeof seed,
                    value: Buffer.isBuffer(seed) ? seed.toString('hex') : seed.toString(),
                    length: Buffer.isBuffer(seed) ? seed.length : 0
                })),
                programId: programId.toString(),
                resultPDA: result[0].toString(),
                bump: result[1],
                timestamp: Date.now(),
                async: true
            };
            
            this.interceptedSeeds.push(seedsInfo);
            
            console.log(`🔍 ПЕРЕХВАЧЕН ASYNC PDA:`);
            console.log(`   Program ID: ${programId.toString().slice(0,8)}...`);
            console.log(`   Result PDA: ${result[0].toString()}`);
            console.log(`   Bump: ${result[1]}`);
            console.log(`   Seeds (${seeds.length}):`);
            seeds.forEach((seed, index) => {
                if (Buffer.isBuffer(seed)) {
                    console.log(`      [${index}] Buffer(${seed.length}): ${seed.toString('hex')}`);
                } else {
                    console.log(`      [${index}] ${typeof seed}: ${seed.toString()}`);
                }
            });
            console.log('');
            
            return result;
        };
        
        console.log('✅ Перехватчик установлен!');
    }

    /**
     * 🔧 СОЗДАНИЕ add_liquidity2 ЧЕРЕЗ SDK
     */
    async createAddLiquidity2ThroughSDK() {
        console.log('🔧 СОЗДАНИЕ add_liquidity2 ЧЕРЕЗ METEORA SDK');
        console.log('==========================================');
        
        try {
            // Создаем тестового пользователя
            const testUser = Keypair.generate();
            console.log(`👤 Тестовый пользователь: ${testUser.publicKey.toString()}`);
            
            // Создаем DLMM объект
            console.log(`📊 Создание DLMM объекта для пула: ${this.SUCCESS_DATA.lbPair}`);
            
            const dlmm = await DLMM.create(
                this.connection,
                new PublicKey(this.SUCCESS_DATA.lbPair)
            );
            
            console.log(`✅ DLMM объект создан`);
            console.log(`   Active Bin ID: ${dlmm.activeBin?.binId || 'неизвестно'}`);
            
            // Создаем параметры для добавления ликвидности (ИСПРАВЛЕНО: используем BN.js!)
            const liquidityParams = {
                positionPubKey: new PublicKey(this.SUCCESS_DATA.position),
                user: testUser.publicKey,
                totalXAmount: new BN(this.SUCCESS_DATA.amountX.toString()),  // 🔧 ИСПРАВЛЕНО: BN.js
                totalYAmount: new BN(this.SUCCESS_DATA.amountY.toString()),  // 🔧 ИСПРАВЛЕНО: BN.js
                strategy: {
                    maxBinId: this.SUCCESS_DATA.activeBinId + 10,
                    minBinId: this.SUCCESS_DATA.activeBinId - 10,
                    strategyType: 'SpotImBalanced'
                }
            };
            
            console.log(`📊 Параметры ликвидности:`);
            console.log(`   Position: ${liquidityParams.positionPubKey.toString()}`);
            console.log(`   User: ${liquidityParams.user.toString()}`);
            console.log(`   Amount X: ${liquidityParams.totalXAmount.toString()} (тип: ${liquidityParams.totalXAmount.constructor.name})`);
            console.log(`   Amount Y: ${liquidityParams.totalYAmount.toString()} (тип: ${liquidityParams.totalYAmount.constructor.name})`);
            console.log(`   Min Bin: ${liquidityParams.strategy.minBinId}`);
            console.log(`   Max Bin: ${liquidityParams.strategy.maxBinId}`);

            // 🔧 ПРОВЕРКА ТИПОВ (для отладки)
            console.log(`\n🔧 ПРОВЕРКА ТИПОВ:`);
            console.log(`   totalXAmount instanceof BN: ${liquidityParams.totalXAmount instanceof BN}`);
            console.log(`   totalYAmount instanceof BN: ${liquidityParams.totalYAmount instanceof BN}`);
            console.log(`   totalXAmount.isZero доступен: ${typeof liquidityParams.totalXAmount.isZero === 'function'}`);
            console.log(`   totalYAmount.isZero доступен: ${typeof liquidityParams.totalYAmount.isZero === 'function'}`);
            
            // Создаем инструкцию через SDK
            console.log(`\n🚀 СОЗДАНИЕ ИНСТРУКЦИИ add_liquidity2...`);
            
            const addLiquidityTx = await dlmm.addLiquidityByStrategy(liquidityParams);
            
            console.log(`✅ Инструкция создана через SDK!`);
            console.log(`   Инструкций: ${addLiquidityTx.instructions?.length || 0}`);
            console.log(`   Signers: ${addLiquidityTx.signers?.length || 0}`);
            
            return addLiquidityTx;
            
        } catch (error) {
            console.error(`❌ Ошибка создания через SDK: ${error.message}`);
            console.error(`📋 Stack trace:`, error.stack);
            return null;
        }
    }

    /**
     * 📊 АНАЛИЗ ПЕРЕХВАЧЕННЫХ SEEDS
     */
    analyzeInterceptedSeeds() {
        console.log('\n📊 АНАЛИЗ ПЕРЕХВАЧЕННЫХ SEEDS');
        console.log('==========================================');
        
        if (this.interceptedSeeds.length === 0) {
            console.log('❌ Не было перехвачено ни одного PDA!');
            return;
        }
        
        console.log(`✅ Перехвачено ${this.interceptedSeeds.length} PDA генераций:`);
        
        this.interceptedSeeds.forEach((seedInfo, index) => {
            console.log(`\n🔍 PDA #${index + 1}:`);
            console.log(`   Result: ${seedInfo.resultPDA}`);
            console.log(`   Program: ${seedInfo.programId.slice(0,8)}...`);
            console.log(`   Bump: ${seedInfo.bump}`);
            console.log(`   Seeds: ${seedInfo.seeds.length} элементов`);
            
            // Проверяем совпадения с известными адресами
            if (seedInfo.resultPDA === this.SUCCESS_DATA.position) {
                console.log(`   🎯 ЭТО POSITION PDA!`);
            } else if (seedInfo.resultPDA === this.SUCCESS_DATA.binArrayBitmapExtension) {
                console.log(`   🎯 ЭТО BIN ARRAY BITMAP EXTENSION PDA!`);
            } else if (seedInfo.resultPDA === this.OUR_DATA.position) {
                console.log(`   🎯 ЭТО НАШ POSITION PDA!`);
            } else if (seedInfo.resultPDA === this.OUR_DATA.binArrayBitmapExtension) {
                console.log(`   🎯 ЭТО НАШ BIN ARRAY BITMAP EXTENSION PDA!`);
            }
            
            seedInfo.seeds.forEach((seed, seedIndex) => {
                console.log(`      [${seedIndex}] ${seed.type}(${seed.length}): ${seed.value}`);
            });
        });
    }

    /**
     * 📋 ГЕНЕРАЦИЯ ФОРМУЛ
     */
    generateFormulas() {
        console.log('\n📋 ГЕНЕРАЦИЯ ФОРМУЛ ДЛЯ ИНТЕГРАЦИИ');
        console.log('==========================================');
        
        // Ищем формулы для известных PDA
        const positionSeeds = this.interceptedSeeds.find(s => 
            s.resultPDA === this.SUCCESS_DATA.position || 
            s.resultPDA === this.OUR_DATA.position
        );
        
        const bitmapSeeds = this.interceptedSeeds.find(s => 
            s.resultPDA === this.SUCCESS_DATA.binArrayBitmapExtension || 
            s.resultPDA === this.OUR_DATA.binArrayBitmapExtension
        );
        
        if (positionSeeds) {
            console.log(`\n✅ ФОРМУЛА ДЛЯ POSITION PDA:`);
            console.log(`const [positionPDA, bump] = PublicKey.findProgramAddressSync([`);
            positionSeeds.seeds.forEach((seed, index) => {
                if (seed.type === 'Buffer') {
                    // Пробуем определить тип буфера
                    if (seed.length <= 32 && /^[a-f0-9]+$/.test(seed.value)) {
                        try {
                            const asString = Buffer.from(seed.value, 'hex').toString('utf8');
                            if (/^[a-zA-Z_]+$/.test(asString)) {
                                console.log(`    Buffer.from("${asString}"),`);
                            } else {
                                console.log(`    Buffer.from("${seed.value}", "hex"),`);
                            }
                        } catch (e) {
                            console.log(`    Buffer.from("${seed.value}", "hex"),`);
                        }
                    } else {
                        console.log(`    /* Buffer(${seed.length}): ${seed.value} */,`);
                    }
                } else {
                    console.log(`    ${seed.value},`);
                }
            });
            console.log(`], METEORA_DLMM_PROGRAM);`);
        }
        
        if (bitmapSeeds) {
            console.log(`\n✅ ФОРМУЛА ДЛЯ BIN ARRAY BITMAP EXTENSION PDA:`);
            console.log(`const [bitmapPDA, bump] = PublicKey.findProgramAddressSync([`);
            bitmapSeeds.seeds.forEach((seed, index) => {
                if (seed.type === 'Buffer') {
                    try {
                        const asString = Buffer.from(seed.value, 'hex').toString('utf8');
                        if (/^[a-zA-Z_]+$/.test(asString)) {
                            console.log(`    Buffer.from("${asString}"),`);
                        } else {
                            console.log(`    Buffer.from("${seed.value}", "hex"),`);
                        }
                    } catch (e) {
                        console.log(`    Buffer.from("${seed.value}", "hex"),`);
                    }
                } else {
                    console.log(`    ${seed.value},`);
                }
            });
            console.log(`], METEORA_DLMM_PROGRAM);`);
        }
    }

    /**
     * 🚀 ПОЛНЫЙ АНАЛИЗ
     */
    async runFullAnalysis() {
        console.log('🚀 ПОЛНЫЙ АНАЛИЗ METEORA SDK SEEDS');
        console.log('==========================================\n');
        
        // Устанавливаем перехватчик
        this.setupSeedsInterceptor();
        
        // Создаем инструкцию через SDK
        const result = await this.createAddLiquidity2ThroughSDK();
        
        // Анализируем перехваченные seeds
        this.analyzeInterceptedSeeds();
        
        // Генерируем формулы
        this.generateFormulas();
        
        return {
            success: result !== null,
            interceptedSeeds: this.interceptedSeeds,
            sdkResult: result
        };
    }
}

// Запуск анализа
async function main() {
    const interceptor = new MeteoraSDKSeedsInterceptor();
    const result = await interceptor.runFullAnalysis();
    
    if (result.success) {
        console.log(`\n🎉 АНАЛИЗ ЗАВЕРШЕН УСПЕШНО!`);
        console.log(`✅ Перехвачено ${result.interceptedSeeds.length} PDA генераций`);
        console.log(`✅ SDK инструкция создана`);
        process.exit(0);
    } else {
        console.log(`\n❌ АНАЛИЗ НЕ УДАЛСЯ!`);
        process.exit(1);
    }
}

main().catch(console.error);
