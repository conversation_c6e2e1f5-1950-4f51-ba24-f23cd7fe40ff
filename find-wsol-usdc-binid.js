/**
 * 🔍 ПОИСК ПРАВИЛЬНОГО BIN ID ДЛЯ WSOL-USDC
 * Используем найденную формулу для поиска bin ID
 */

const { PublicKey } = require('@solana/web3.js');
const BN = require('bn.js');

console.log('🔍 ПОИСК ПРАВИЛЬНОГО BIN ID ДЛЯ WSOL-USDC\n');

function findWsolUsdcBinId() {
    try {
        console.log('📊 ПОИСК BIN ID ДЛЯ WSOL-USDC С ПРАВИЛЬНОЙ ФОРМУЛОЙ');
        console.log('==========================================');

        const METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        const WSOL_USDC_POOL = new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6');
        const TARGET_PDA = '59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li';

        console.log(`🎯 Цель: ${TARGET_PDA}`);
        console.log(`📊 LB Pair: ${WSOL_USDC_POOL.toString()}`);
        console.log(`🔧 Формула: Buffer.from("bin_array") + lbPair + binId_as_BN_LE`);

        // Правильная функция генерации (найдена через анализ)
        function generateBitmapExtension(lbPairAddress, binId) {
            // Используем BN.js little-endian (как в SDK)
            const binIdBuffer = new BN(binId).toArrayLike(Buffer, 'le', 8);
            
            const seeds = [
                Buffer.from("bin_array"),
                lbPairAddress.toBuffer(),
                binIdBuffer
            ];

            const [pda, bump] = PublicKey.findProgramAddressSync(seeds, METEORA_DLMM_PROGRAM);
            return { pda, bump, binIdHex: binIdBuffer.toString('hex') };
        }

        console.log('\n🔍 РАСШИРЕННЫЙ ПОИСК BIN ID:');
        console.log('==========================================');

        // Расширенный диапазон поиска
        const ranges = [
            // Диапазон 1: Около нуля
            { start: -100, end: 100, step: 1, name: 'Около нуля' },
            
            // Диапазон 2: Около активного bin ID из логов
            { start: -4600, end: -4400, step: 1, name: 'Около активного bin ID' },
            
            // Диапазон 3: Большие отрицательные значения
            { start: -10000, end: -1000, step: 100, name: 'Большие отрицательные' },
            
            // Диапазон 4: Большие положительные значения
            { start: 1000, end: 10000, step: 100, name: 'Большие положительные' },
            
            // Диапазон 5: Очень большие значения
            { start: -100000, end: -10000, step: 1000, name: 'Очень большие отрицательные' },
            { start: 10000, end: 100000, step: 1000, name: 'Очень большие положительные' }
        ];

        let foundBinId = null;
        let totalTested = 0;

        ranges.forEach((range, rangeIndex) => {
            console.log(`\n📊 ДИАПАЗОН ${rangeIndex + 1}: ${range.name} (${range.start} до ${range.end}, шаг ${range.step})`);
            
            let rangeFound = false;
            let rangeTested = 0;

            for (let binId = range.start; binId <= range.end; binId += range.step) {
                try {
                    const result = generateBitmapExtension(WSOL_USDC_POOL, binId);
                    rangeTested++;
                    totalTested++;

                    if (result.pda.toString() === TARGET_PDA) {
                        foundBinId = {
                            binId,
                            pda: result.pda.toString(),
                            bump: result.bump,
                            binIdHex: result.binIdHex,
                            range: range.name
                        };
                        
                        console.log(`   🎉 НАЙДЕН! Bin ID: ${binId}`);
                        console.log(`      PDA: ${result.pda.toString()}`);
                        console.log(`      Bump: ${result.bump}`);
                        console.log(`      Bin ID Hex: ${result.binIdHex}`);
                        console.log(`      Диапазон: ${range.name}`);
                        
                        rangeFound = true;
                        break;
                    }

                    // Логируем прогресс каждые 100 тестов
                    if (rangeTested % 100 === 0) {
                        console.log(`      Протестировано: ${rangeTested} в диапазоне, ${totalTested} всего...`);
                    }

                } catch (error) {
                    // Игнорируем ошибки генерации
                }
            }

            if (!rangeFound) {
                console.log(`   ❌ Не найден в диапазоне ${range.name} (протестировано: ${rangeTested})`);
            }

            // Если нашли, прерываем поиск
            if (foundBinId) {
                return;
            }
        });

        console.log('\n📊 РЕЗУЛЬТАТЫ ПОИСКА:');
        console.log('==========================================');
        console.log(`📊 Всего протестировано bin ID: ${totalTested}`);

        if (foundBinId) {
            console.log(`🎉 УСПЕХ! НАЙДЕН ПРАВИЛЬНЫЙ BIN ID:`);
            console.log(`   ✅ Bin ID: ${foundBinId.binId}`);
            console.log(`   ✅ PDA: ${foundBinId.pda}`);
            console.log(`   ✅ Bump: ${foundBinId.bump}`);
            console.log(`   ✅ Bin ID Hex: ${foundBinId.binIdHex}`);
            console.log(`   ✅ Найден в диапазоне: ${foundBinId.range}`);

            console.log('\n📋 ПРАВИЛЬНАЯ ФУНКЦИЯ ДЛЯ ИНТЕГРАЦИИ:');
            console.log('==========================================');
            console.log(`
// 🎯 ИСПРАВЛЕННАЯ ФУНКЦИЯ getBinArrayBitmapExtension
getBinArrayBitmapExtension(lbPairAddress, binId = 0) {
    try {
        // 🔧 ПРАВИЛЬНОЕ КОДИРОВАНИЕ (найдено через точный анализ!)
        const binIdBuffer = new BN(binId).toArrayLike(Buffer, 'le', 8);
        
        // 🔧 ПРАВИЛЬНЫЙ ПОРЯДОК SEEDS
        const seeds = [
            Buffer.from("bin_array"),        // 1. Строка "bin_array"
            lbPairAddress.toBuffer(),        // 2. LB Pair address
            binIdBuffer                      // 3. Bin ID как BN little-endian 8 байт
        ];

        const [pda, bump] = PublicKey.findProgramAddressSync(seeds, this.METEORA_DLMM_PROGRAM);
        
        console.log(\`✅ Bitmap Extension PDA: \${pda.toString()}\`);
        return pda;

    } catch (error) {
        console.error(\`❌ Ошибка в getBinArrayBitmapExtension: \${error.message}\`);
        throw error;
    }
}

// 🔧 ИЗВЕСТНЫЕ BIN ID ДЛЯ ПУЛОВ:
const KNOWN_BIN_IDS = {
    'E9kQAzGFvgC7xtgNGCe63meEKBFw2T5yNjzgbJAtNcSJ': -8,    // Kai-WSOL
    '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6': ${foundBinId.binId}     // WSOL-USDC
};
`);

        } else {
            console.log(`❌ НЕ НАЙДЕН ПРАВИЛЬНЫЙ BIN ID!`);
            console.log(`🔧 РЕКОМЕНДАЦИИ:`);
            console.log(`   1. Расширить диапазон поиска`);
            console.log(`   2. Проверить другие способы кодирования`);
            console.log(`   3. Использовать статические адреса как fallback`);
            console.log(`   4. Проверить что целевой PDA правильный`);
        }

        return foundBinId;

    } catch (error) {
        console.error(`❌ КРИТИЧЕСКАЯ ОШИБКА В ПОИСКЕ:`, error.message);
        console.error(`📋 Stack trace:`, error.stack);
        return null;
    }
}

// Запуск поиска
const result = findWsolUsdcBinId();

if (result) {
    console.log(`\n🎉 ПОИСК ЗАВЕРШЕН УСПЕШНО!`);
    console.log(`✅ Найден bin ID для WSOL-USDC: ${result.binId}`);
    console.log(`✅ Готово к интеграции в основной код`);
    process.exit(0);
} else {
    console.log(`\n❌ ПОИСК НЕ ДАЛ РЕЗУЛЬТАТОВ!`);
    console.log(`❌ Требуется дополнительный анализ или использование fallback`);
    process.exit(1);
}
