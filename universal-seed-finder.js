/**
 * 🔍 УНИВЕРСАЛЬНЫЙ ПОИСК SEEDS ДЛЯ METEORA DLMM PDA
 * Автоматически находит правильные seeds для любого PDA
 */

const { PublicKey } = require('@solana/web3.js');

console.log('🔍 УНИВЕРСАЛЬНЫЙ ПОИСК SEEDS ДЛЯ METEORA DLMM\n');

class UniversalSeedFinder {
    constructor() {
        this.METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        this.WSOL_USDC_POOL = new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6');
        
        // Известные правильные адреса из успешных транзакций
        this.KNOWN_ADDRESSES = {
            binArrayBitmapExtension: '59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li',
            // Добавляем другие известные адреса по мере необходимости
        };
    }

    /**
     * 🔍 ПОИСК SEEDS ДЛЯ BIN ARRAY BITMAP EXTENSION
     */
    findBinArrayBitmapExtensionSeed() {
        console.log('🔍 ПОИСК SEEDS ДЛЯ BIN ARRAY BITMAP EXTENSION');
        console.log('==========================================');
        
        const targetAddress = this.KNOWN_ADDRESSES.binArrayBitmapExtension;
        console.log(`🎯 Целевой адрес: ${targetAddress}`);
        console.log(`📊 Пул: ${this.WSOL_USDC_POOL.toString()}`);
        console.log(`🔧 Программа: ${this.METEORA_DLMM_PROGRAM.toString()}`);

        // Список возможных seeds из анализа Meteora кода
        const possibleSeeds = [
            // Простые варианты
            'bitmap',
            'bin_array_bitmap',
            'bin_array_bitmap_ext',
            'bin_array_bitmap_extension',
            'bitmap_extension',
            'extension',
            'bitmap_ext',
            
            // Варианты с подчеркиваниями
            'bin_array_bitmap_ext',
            'bin_array_bitmap_extension',
            'bitmap_extension',
            
            // Сокращенные варианты
            'bmp',
            'bmp_ext',
            'ba_bitmap',
            'ba_bmp',
            
            // Meteora специфичные
            'meteora_bitmap',
            'dlmm_bitmap',
            'lb_bitmap'
        ];

        console.log(`\n📊 ТЕСТИРОВАНИЕ ${possibleSeeds.length} ВОЗМОЖНЫХ SEEDS:`);
        console.log('==========================================');

        let foundSeed = null;
        let foundPDA = null;

        possibleSeeds.forEach((seed, index) => {
            try {
                const [pda] = PublicKey.findProgramAddressSync([
                    Buffer.from(seed),
                    this.WSOL_USDC_POOL.toBuffer()
                ], this.METEORA_DLMM_PROGRAM);

                const isCorrect = pda.toString() === targetAddress;
                console.log(`   ${(index + 1).toString().padStart(2, '0')}. "${seed}": ${pda.toString().slice(0, 8)}...${pda.toString().slice(-8)}`);
                console.log(`       Совпадает: ${isCorrect ? '✅ ДА' : '❌ НЕТ'}`);

                if (isCorrect) {
                    foundSeed = seed;
                    foundPDA = pda;
                    console.log(`       🎉 НАЙДЕН ПРАВИЛЬНЫЙ SEED!`);
                }
            } catch (error) {
                console.log(`   ${(index + 1).toString().padStart(2, '0')}. "${seed}": ❌ ОШИБКА - ${error.message}`);
            }
        });

        return { seed: foundSeed, pda: foundPDA };
    }

    /**
     * 🔍 РАСШИРЕННЫЙ ПОИСК С РАЗЛИЧНЫМИ КОМБИНАЦИЯМИ
     */
    findWithAdvancedCombinations() {
        console.log('\n🔍 РАСШИРЕННЫЙ ПОИСК С КОМБИНАЦИЯМИ');
        console.log('==========================================');
        
        const targetAddress = this.KNOWN_ADDRESSES.binArrayBitmapExtension;
        
        // Тестируем различные комбинации параметров
        const advancedTests = [
            {
                name: 'Только seed без pool',
                seeds: [Buffer.from('bitmap')],
            },
            {
                name: 'Pool + seed (обратный порядок)',
                seeds: [this.WSOL_USDC_POOL.toBuffer(), Buffer.from('bitmap')],
            },
            {
                name: 'Seed + program ID',
                seeds: [Buffer.from('bitmap'), this.METEORA_DLMM_PROGRAM.toBuffer()],
            },
            {
                name: 'Множественные seeds',
                seeds: [Buffer.from('bin_array'), Buffer.from('bitmap'), this.WSOL_USDC_POOL.toBuffer()],
            },
            {
                name: 'С числовым суффиксом',
                seeds: [Buffer.from('bitmap'), this.WSOL_USDC_POOL.toBuffer(), Buffer.from([0])],
            }
        ];

        let foundAdvanced = null;

        advancedTests.forEach((test, index) => {
            try {
                const [pda] = PublicKey.findProgramAddressSync(test.seeds, this.METEORA_DLMM_PROGRAM);
                const isCorrect = pda.toString() === targetAddress;
                
                console.log(`   ${index + 1}. ${test.name}: ${pda.toString().slice(0, 8)}...${pda.toString().slice(-8)}`);
                console.log(`      Совпадает: ${isCorrect ? '✅ ДА' : '❌ НЕТ'}`);

                if (isCorrect) {
                    foundAdvanced = test;
                    console.log(`      🎉 НАЙДЕНА ПРАВИЛЬНАЯ КОМБИНАЦИЯ!`);
                }
            } catch (error) {
                console.log(`   ${index + 1}. ${test.name}: ❌ ОШИБКА - ${error.message}`);
            }
        });

        return foundAdvanced;
    }

    /**
     * 🔍 ПОЛНЫЙ АНАЛИЗ И ГЕНЕРАЦИЯ КОДА
     */
    runFullAnalysis() {
        console.log('🔍 ПОЛНЫЙ АНАЛИЗ SEEDS ДЛЯ METEORA DLMM');
        console.log('==========================================\n');

        // Основной поиск
        const basicResult = this.findBinArrayBitmapExtensionSeed();
        
        // Расширенный поиск
        const advancedResult = this.findWithAdvancedCombinations();

        console.log('\n🎯 ИТОГОВЫЕ РЕЗУЛЬТАТЫ:');
        console.log('==========================================');

        if (basicResult.seed) {
            console.log(`🎉 ОСНОВНОЙ ПОИСК: УСПЕХ!`);
            console.log(`   ✅ Seed: "${basicResult.seed}"`);
            console.log(`   ✅ PDA: ${basicResult.pda.toString()}`);
            
            console.log(`\n📋 КОД ДЛЯ ИСПРАВЛЕНИЯ:`);
            console.log(`const [bitmapExtensionPDA] = PublicKey.findProgramAddressSync([`);
            console.log(`    Buffer.from("${basicResult.seed}"),`);
            console.log(`    poolAddress.toBuffer()`);
            console.log(`], this.METEORA_DLMM_PROGRAM);`);
            
            return basicResult;
        } else if (advancedResult) {
            console.log(`🎉 РАСШИРЕННЫЙ ПОИСК: УСПЕХ!`);
            console.log(`   ✅ Комбинация: ${advancedResult.name}`);
            console.log(`   ✅ Seeds: ${advancedResult.seeds.length} элементов`);
            
            return advancedResult;
        } else {
            console.log(`❌ ПОИСК НЕ УВЕНЧАЛСЯ УСПЕХОМ!`);
            console.log(`❌ Не найден правильный способ генерации PDA`);
            console.log(`\n🔧 РЕКОМЕНДАЦИИ:`);
            console.log(`1. Проверить исходный код Meteora DLMM на GitHub`);
            console.log(`2. Использовать Solana Explorer для анализа аккаунта`);
            console.log(`3. Обратиться к документации Meteora`);
            console.log(`4. Проверить IDL файл программы`);
            
            return null;
        }
    }
}

// Запуск анализа
const finder = new UniversalSeedFinder();
const result = finder.runFullAnalysis();

if (result) {
    console.log(`\n✅ ПРОБЛЕМА РЕШЕНА! Используйте найденный seed в коде.`);
    process.exit(0);
} else {
    console.log(`\n❌ ПРОБЛЕМА НЕ РЕШЕНА! Требуется дополнительное исследование.`);
    process.exit(1);
}
