/**
 * 🔍 REVERSE ENGINEERING METEORA DLMM SEEDS
 * Извлекаем правильные seeds из успешной транзакции
 */

const { PublicKey } = require('@solana/web3.js');

console.log('🔍 REVERSE ENGINEERING METEORA DLMM SEEDS\n');

class MeteoraSeeedReverseEngineer {
    constructor() {
        this.METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        
        // Данные из успешной транзакции
        this.SUCCESS_DATA = {
            position: '9ZxeGGgXYsFykPhBkP5tizx5WDWB5QQJjzyahRwwTGxb',
            lbPair: 'E9kQAzGFvgC7xtgNGCe63meEKBFw2T5yNjzgbJAtNcSJ',
            binArrayBitmapExtension: 'GHhTdx41vXRhzUGxCAUhpYxsi2P5668JYW7R38fuxkDQ',
            user: 'H6uwL8TyV54xAUnarAwMLnRQtEpAdRDzoEjkfFbyF8vS',
            eventAuthority: 'D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6'
        };
        
        // Наши данные (WSOL-USDC)
        this.OUR_DATA = {
            lbPair: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
            binArrayBitmapExtension: '59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li',
            position: 'Gbv33r6KGduHGWb8D5m4jhJMTqnGE4oaMun7ToHLr1UC',
            user: 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV'
        };
    }

    /**
     * 🔍 ПОИСК SEEDS ДЛЯ BIN ARRAY BITMAP EXTENSION
     */
    findBinArrayBitmapExtensionSeeds() {
        console.log('🔍 ПОИСК SEEDS ДЛЯ BIN ARRAY BITMAP EXTENSION');
        console.log('==========================================');
        
        const targetAddress = this.SUCCESS_DATA.binArrayBitmapExtension;
        const lbPair = new PublicKey(this.SUCCESS_DATA.lbPair);
        
        console.log(`🎯 Целевой адрес: ${targetAddress}`);
        console.log(`📊 LB Pair: ${lbPair.toString()}`);

        const possibleSeeds = [
            'bitmap',
            'bin_array_bitmap',
            'bin_array_bitmap_ext',
            'bin_array_bitmap_extension',
            'bitmap_extension',
            'extension',
            'bitmap_ext',
            'ba_bitmap',
            'ba_bitmap_ext',
            'dlmm_bitmap',
            'meteora_bitmap'
        ];

        let foundSeed = null;

        possibleSeeds.forEach((seed, index) => {
            try {
                const [pda] = PublicKey.findProgramAddressSync([
                    Buffer.from(seed),
                    lbPair.toBuffer()
                ], this.METEORA_DLMM_PROGRAM);

                const isCorrect = pda.toString() === targetAddress;
                console.log(`   ${(index + 1).toString().padStart(2, '0')}. "${seed}": ${pda.toString()}`);
                console.log(`       Совпадает: ${isCorrect ? '✅ ДА' : '❌ НЕТ'}`);

                if (isCorrect) {
                    foundSeed = seed;
                    console.log(`       🎉 НАЙДЕН ПРАВИЛЬНЫЙ SEED ДЛЯ BITMAP EXTENSION!`);
                }
            } catch (error) {
                console.log(`   ${(index + 1).toString().padStart(2, '0')}. "${seed}": ❌ ОШИБКА - ${error.message}`);
            }
        });

        return foundSeed;
    }

    /**
     * 🔍 ПОИСК SEEDS ДЛЯ POSITION
     */
    findPositionSeeds() {
        console.log('\n🔍 ПОИСК SEEDS ДЛЯ POSITION');
        console.log('==========================================');
        
        const targetAddress = this.SUCCESS_DATA.position;
        const lbPair = new PublicKey(this.SUCCESS_DATA.lbPair);
        const user = new PublicKey(this.SUCCESS_DATA.user);
        
        console.log(`🎯 Целевой адрес: ${targetAddress}`);
        console.log(`📊 LB Pair: ${lbPair.toString()}`);
        console.log(`👤 User: ${user.toString()}`);

        const positionTests = [
            {
                name: 'position + user + lbPair',
                seeds: [Buffer.from('position'), user.toBuffer(), lbPair.toBuffer()]
            },
            {
                name: 'position + lbPair + user',
                seeds: [Buffer.from('position'), lbPair.toBuffer(), user.toBuffer()]
            },
            {
                name: 'user_position + user + lbPair',
                seeds: [Buffer.from('user_position'), user.toBuffer(), lbPair.toBuffer()]
            },
            {
                name: 'lb_position + user + lbPair',
                seeds: [Buffer.from('lb_position'), user.toBuffer(), lbPair.toBuffer()]
            },
            {
                name: 'position + user',
                seeds: [Buffer.from('position'), user.toBuffer()]
            },
            {
                name: 'position + lbPair',
                seeds: [Buffer.from('position'), lbPair.toBuffer()]
            }
        ];

        let foundPositionSeed = null;

        positionTests.forEach((test, index) => {
            try {
                const [pda] = PublicKey.findProgramAddressSync(test.seeds, this.METEORA_DLMM_PROGRAM);
                const isCorrect = pda.toString() === targetAddress;
                
                console.log(`   ${index + 1}. ${test.name}: ${pda.toString()}`);
                console.log(`      Совпадает: ${isCorrect ? '✅ ДА' : '❌ НЕТ'}`);

                if (isCorrect) {
                    foundPositionSeed = test;
                    console.log(`      🎉 НАЙДЕН ПРАВИЛЬНЫЙ SEED ДЛЯ POSITION!`);
                }
            } catch (error) {
                console.log(`   ${index + 1}. ${test.name}: ❌ ОШИБКА - ${error.message}`);
            }
        });

        return foundPositionSeed;
    }

    /**
     * 🔍 ПРОВЕРКА НАЙДЕННЫХ SEEDS НА НАШИХ ДАННЫХ
     */
    validateSeedsOnOurData(bitmapSeed, positionSeed) {
        console.log('\n🔍 ПРОВЕРКА НАЙДЕННЫХ SEEDS НА НАШИХ ДАННЫХ');
        console.log('==========================================');
        
        const ourLbPair = new PublicKey(this.OUR_DATA.lbPair);
        const ourUser = new PublicKey(this.OUR_DATA.user);
        
        console.log(`📊 Наш LB Pair: ${ourLbPair.toString()}`);
        console.log(`👤 Наш User: ${ourUser.toString()}`);

        let validationResults = {
            bitmap: false,
            position: false
        };

        // Проверяем Bitmap Extension
        if (bitmapSeed) {
            try {
                const [ourBitmapPDA] = PublicKey.findProgramAddressSync([
                    Buffer.from(bitmapSeed),
                    ourLbPair.toBuffer()
                ], this.METEORA_DLMM_PROGRAM);

                const bitmapMatches = ourBitmapPDA.toString() === this.OUR_DATA.binArrayBitmapExtension;
                console.log(`\n✅ BITMAP EXTENSION:`);
                console.log(`   Ожидаемый: ${this.OUR_DATA.binArrayBitmapExtension}`);
                console.log(`   Получили:  ${ourBitmapPDA.toString()}`);
                console.log(`   Совпадает: ${bitmapMatches ? '✅ ДА' : '❌ НЕТ'}`);
                
                validationResults.bitmap = bitmapMatches;
            } catch (error) {
                console.log(`❌ Ошибка проверки bitmap: ${error.message}`);
            }
        }

        // Проверяем Position
        if (positionSeed) {
            try {
                const [ourPositionPDA] = PublicKey.findProgramAddressSync(positionSeed.seeds, this.METEORA_DLMM_PROGRAM);

                const positionMatches = ourPositionPDA.toString() === this.OUR_DATA.position;
                console.log(`\n✅ POSITION:`);
                console.log(`   Ожидаемый: ${this.OUR_DATA.position}`);
                console.log(`   Получили:  ${ourPositionPDA.toString()}`);
                console.log(`   Совпадает: ${positionMatches ? '✅ ДА' : '❌ НЕТ'}`);
                
                validationResults.position = positionMatches;
            } catch (error) {
                console.log(`❌ Ошибка проверки position: ${error.message}`);
            }
        }

        return validationResults;
    }

    /**
     * 🔍 ПОЛНЫЙ АНАЛИЗ И ГЕНЕРАЦИЯ КОДА
     */
    runFullAnalysis() {
        console.log('🔍 ПОЛНЫЙ REVERSE ENGINEERING METEORA DLMM SEEDS');
        console.log('==========================================\n');

        // Поиск seeds
        const bitmapSeed = this.findBinArrayBitmapExtensionSeeds();
        const positionSeed = this.findPositionSeeds();
        
        // Проверка на наших данных
        const validation = this.validateSeedsOnOurData(bitmapSeed, positionSeed);

        console.log('\n🎯 ИТОГОВЫЕ РЕЗУЛЬТАТЫ:');
        console.log('==========================================');

        if (bitmapSeed || positionSeed) {
            console.log(`🎉 SEEDS НАЙДЕНЫ!`);
            
            if (bitmapSeed) {
                console.log(`\n✅ BIN ARRAY BITMAP EXTENSION:`);
                console.log(`   Seed: "${bitmapSeed}"`);
                console.log(`   Валидация: ${validation.bitmap ? '✅ ПРОШЛА' : '❌ НЕ ПРОШЛА'}`);
            }
            
            if (positionSeed) {
                console.log(`\n✅ POSITION:`);
                console.log(`   Seeds: ${positionSeed.name}`);
                console.log(`   Валидация: ${validation.position ? '✅ ПРОШЛА' : '❌ НЕ ПРОШЛА'}`);
            }

            return { bitmapSeed, positionSeed, validation };
        } else {
            console.log(`❌ SEEDS НЕ НАЙДЕНЫ!`);
            return null;
        }
    }
}

// Запуск анализа
const engineer = new MeteoraSeeedReverseEngineer();
const result = engineer.runFullAnalysis();

if (result) {
    console.log(`\n📋 ГОТОВЫЕ ФОРМУЛЫ ДЛЯ КОДА:`);
    console.log('==========================================');
    
    if (result.bitmapSeed) {
        console.log(`\n// BIN ARRAY BITMAP EXTENSION`);
        console.log(`const [bitmapExtensionPDA] = PublicKey.findProgramAddressSync([`);
        console.log(`    Buffer.from("${result.bitmapSeed}"),`);
        console.log(`    lbPairAddress.toBuffer()`);
        console.log(`], METEORA_DLMM_PROGRAM);`);
    }
    
    if (result.positionSeed) {
        console.log(`\n// POSITION`);
        console.log(`const [positionPDA] = PublicKey.findProgramAddressSync([`);
        result.positionSeed.seeds.forEach((seed, index) => {
            if (Buffer.isBuffer(seed)) {
                if (seed.toString() === 'position') {
                    console.log(`    Buffer.from("position"),`);
                } else {
                    console.log(`    ${index === 1 ? 'userAddress' : 'lbPairAddress'}.toBuffer(),`);
                }
            }
        });
        console.log(`], METEORA_DLMM_PROGRAM);`);
    }
    
    process.exit(0);
} else {
    console.log(`\n❌ АНАЛИЗ НЕ УВЕНЧАЛСЯ УСПЕХОМ!`);
    process.exit(1);
}
