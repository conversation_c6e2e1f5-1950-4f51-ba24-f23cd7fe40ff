/**
 * 🎯 ГЕНЕРАТОР PDA ДЛЯ WSOL-USDC (БЕЗ RPC ПРОВЕРОК)
 * Генерирует все необходимые PDA и экспортирует в файлы
 */

const { PublicKey } = require('@solana/web3.js');
const BN = require('bn.js');
const fs = require('fs');
const path = require('path');

console.log('🎯 ГЕНЕРАТОР PDA ДЛЯ WSOL-USDC\n');

class WSOLUSDCPDAGenerator {
    constructor() {
        this.METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        
        // 🎯 WSOL-USDC СПЕЦИФИЧНЫЕ КОНСТАНТЫ
        this.WSOL_USDC_LB_PAIR = new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6'); // НАШ ОСНОВНОЙ ПУЛ!
        this.WSOL_MINT = new PublicKey('So11111111111111111111111111111111111111112');
        this.USDC_MINT = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');
        
        // НАШ ПОЛЬЗОВАТЕЛЬ
        this.USER_PUBLIC_KEY = new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV');
        
        // ИЗВЕСТНЫЕ ДАННЫЕ ИЗ УСПЕШНЫХ ТРАНЗАКЦИЙ
        this.KNOWN_DATA = {
            expectedPosition: 'Gbv33r6KGduHGWb8D5m4jhJMTqnGE4oaMun7ToHLr1UC',
            expectedBitmapExtension: '59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li',
            activeBinId: -4517 // Из логов
        };
    }

    /**
     * 🎯 КАНОНИЧНЫЕ ГЕНЕРАТОРЫ PDA ДЛЯ WSOL-USDC
     */
    
    getPositionPDA(user) {
        return PublicKey.findProgramAddressSync(
            [Buffer.from("position"), this.WSOL_USDC_LB_PAIR.toBuffer(), user.toBuffer()],
            this.METEORA_DLMM_PROGRAM
        );
    }

    getBinArrayBitmapExtensionPDA(binId) {
        // 🔧 КАНОНИЧНОЕ КОДИРОВАНИЕ: BN.js с toTwos для signed i64 LE
        const binIdBuffer = new BN(binId).toTwos(64).toArrayLike(Buffer, 'le', 8);
        return PublicKey.findProgramAddressSync(
            [Buffer.from("bin_array"), this.WSOL_USDC_LB_PAIR.toBuffer(), binIdBuffer],
            this.METEORA_DLMM_PROGRAM
        );
    }

    getEventAuthorityPDA() {
        return PublicKey.findProgramAddressSync(
            [Buffer.from("__event_authority")],
            this.METEORA_DLMM_PROGRAM
        );
    }

    getStrategyPDA(user) {
        return PublicKey.findProgramAddressSync(
            [Buffer.from("strategy"), this.WSOL_USDC_LB_PAIR.toBuffer(), user.toBuffer()],
            this.METEORA_DLMM_PROGRAM
        );
    }

    getLiquidityAccountPDA(user) {
        return PublicKey.findProgramAddressSync(
            [Buffer.from("liquidity_account"), user.toBuffer()],
            this.METEORA_DLMM_PROGRAM
        );
    }

    getGlobalStatePDA() {
        return PublicKey.findProgramAddressSync(
            [Buffer.from("global_state")],
            this.METEORA_DLMM_PROGRAM
        );
    }

    /**
     * 🔍 ПОИСК ПРАВИЛЬНОГО BIN ID ДЛЯ BITMAP EXTENSION
     */
    findCorrectBinIdForBitmapExtension() {
        console.log('🔍 ПОИСК ПРАВИЛЬНОГО BIN ID ДЛЯ BITMAP EXTENSION');
        console.log('------------------------------------------');
        console.log(`🎯 Целевой PDA: ${this.KNOWN_DATA.expectedBitmapExtension}`);

        // Тестируем диапазон вокруг активного bin ID
        const centerBinId = this.KNOWN_DATA.activeBinId;
        const testRange = 100; // ±100 от центра

        for (let offset = -testRange; offset <= testRange; offset++) {
            const binId = centerBinId + offset;
            
            try {
                const [bitmapPDA, bump] = this.getBinArrayBitmapExtensionPDA(binId);
                
                if (bitmapPDA.toString() === this.KNOWN_DATA.expectedBitmapExtension) {
                    console.log(`🎉 НАЙДЕН ПРАВИЛЬНЫЙ BIN ID: ${binId}`);
                    console.log(`   PDA: ${bitmapPDA.toString()}`);
                    console.log(`   Bump: ${bump}`);
                    console.log(`   Offset от активного: ${offset}`);
                    
                    return { binId, pda: bitmapPDA, bump };
                }
            } catch (error) {
                // Игнорируем ошибки
            }
        }

        console.log(`❌ Правильный bin ID не найден в диапазоне ±${testRange} от ${centerBinId}`);
        
        // Возвращаем активный bin ID как fallback
        const [fallbackPDA, fallbackBump] = this.getBinArrayBitmapExtensionPDA(centerBinId);
        console.log(`🔄 Используем активный bin ID как fallback: ${centerBinId}`);
        
        return { binId: centerBinId, pda: fallbackPDA, bump: fallbackBump };
    }

    /**
     * 🚀 ГЕНЕРАЦИЯ ВСЕХ PDA
     */
    generateAllPDAs() {
        console.log('🚀 ГЕНЕРАЦИЯ ВСЕХ PDA ДЛЯ WSOL-USDC');
        console.log('==========================================');
        console.log(`📊 LB Pair: ${this.WSOL_USDC_LB_PAIR.toString()}`);
        console.log(`👤 User: ${this.USER_PUBLIC_KEY.toString()}`);
        console.log(`🪙 WSOL Mint: ${this.WSOL_MINT.toString()}`);
        console.log(`🪙 USDC Mint: ${this.USDC_MINT.toString()}`);

        // Генерируем все PDA
        const [positionPDA, positionBump] = this.getPositionPDA(this.USER_PUBLIC_KEY);
        const [eventAuthorityPDA, eventAuthorityBump] = this.getEventAuthorityPDA();
        const [strategyPDA, strategyBump] = this.getStrategyPDA(this.USER_PUBLIC_KEY);
        const [liquidityAccountPDA, liquidityAccountBump] = this.getLiquidityAccountPDA(this.USER_PUBLIC_KEY);
        const [globalStatePDA, globalStateBump] = this.getGlobalStatePDA();

        // Поиск правильного bin ID для bitmap extension
        const bitmapResult = this.findCorrectBinIdForBitmapExtension();

        console.log('\n📋 СГЕНЕРИРОВАННЫЕ PDA:');
        console.log('------------------------------------------');
        console.log(`Position PDA:        ${positionPDA.toString()} (bump: ${positionBump})`);
        console.log(`Event Authority:     ${eventAuthorityPDA.toString()} (bump: ${eventAuthorityBump})`);
        console.log(`Strategy PDA:        ${strategyPDA.toString()} (bump: ${strategyBump})`);
        console.log(`Liquidity Account:   ${liquidityAccountPDA.toString()} (bump: ${liquidityAccountBump})`);
        console.log(`Global State:        ${globalStatePDA.toString()} (bump: ${globalStateBump})`);
        console.log(`Bitmap Extension:    ${bitmapResult.pda.toString()} (bump: ${bitmapResult.bump}, binId: ${bitmapResult.binId})`);

        // Проверяем совпадения с известными данными
        console.log('\n🔍 ПРОВЕРКА СОВПАДЕНИЙ С ИЗВЕСТНЫМИ ДАННЫМИ:');
        console.log('------------------------------------------');
        
        const positionMatches = positionPDA.toString() === this.KNOWN_DATA.expectedPosition;
        const bitmapMatches = bitmapResult.pda.toString() === this.KNOWN_DATA.expectedBitmapExtension;
        
        console.log(`Position PDA: ${positionMatches ? '✅ СОВПАДАЕТ' : '❌ НЕ СОВПАДАЕТ'}`);
        if (!positionMatches) {
            console.log(`   Ожидался: ${this.KNOWN_DATA.expectedPosition}`);
            console.log(`   Получили: ${positionPDA.toString()}`);
        }
        
        console.log(`Bitmap Extension: ${bitmapMatches ? '✅ СОВПАДАЕТ' : '❌ НЕ СОВПАДАЕТ'}`);
        if (!bitmapMatches) {
            console.log(`   Ожидался: ${this.KNOWN_DATA.expectedBitmapExtension}`);
            console.log(`   Получили: ${bitmapResult.pda.toString()}`);
        }

        // Собираем все данные
        const pdaData = {
            metadata: {
                lbPair: this.WSOL_USDC_LB_PAIR.toString(),
                user: this.USER_PUBLIC_KEY.toString(),
                wsolMint: this.WSOL_MINT.toString(),
                usdcMint: this.USDC_MINT.toString(),
                timestamp: new Date().toISOString(),
                positionMatches,
                bitmapMatches
            },
            pdas: {
                position: { 
                    address: positionPDA.toString(), 
                    bump: positionBump,
                    matches: positionMatches
                },
                eventAuthority: { 
                    address: eventAuthorityPDA.toString(), 
                    bump: eventAuthorityBump 
                },
                strategy: { 
                    address: strategyPDA.toString(), 
                    bump: strategyBump 
                },
                liquidityAccount: { 
                    address: liquidityAccountPDA.toString(), 
                    bump: liquidityAccountBump 
                },
                globalState: { 
                    address: globalStatePDA.toString(), 
                    bump: globalStateBump 
                },
                bitmapExtension: {
                    address: bitmapResult.pda.toString(),
                    bump: bitmapResult.bump,
                    binId: bitmapResult.binId,
                    matches: bitmapMatches
                }
            },
            knownData: this.KNOWN_DATA
        };

        return pdaData;
    }

    /**
     * 📁 ЭКСПОРТ РЕЗУЛЬТАТОВ
     */
    exportResults(pdaData) {
        console.log('\n📁 ЭКСПОРТ РЕЗУЛЬТАТОВ:');
        console.log('------------------------------------------');

        // Создаем директорию out
        const outDir = path.join(__dirname, 'out');
        if (!fs.existsSync(outDir)) {
            fs.mkdirSync(outDir, { recursive: true });
        }

        // 1. JSON файл
        const jsonPath = path.join(outDir, 'wsol-usdc-pda-complete.json');
        fs.writeFileSync(jsonPath, JSON.stringify(pdaData, null, 2));
        console.log(`✅ JSON экспорт: ${jsonPath}`);

        // 2. JavaScript константы для интеграции
        const jsContent = `/**
 * 🎯 WSOL-USDC PDA КОНСТАНТЫ ДЛЯ ИНТЕГРАЦИИ
 * Сгенерировано: ${pdaData.metadata.timestamp}
 * Position совпадает: ${pdaData.metadata.positionMatches}
 * Bitmap Extension совпадает: ${pdaData.metadata.bitmapMatches}
 */

const { PublicKey } = require('@solana/web3.js');

// 🔧 ОСНОВНЫЕ КОНСТАНТЫ
const METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
const WSOL_USDC_LB_PAIR = new PublicKey('${pdaData.metadata.lbPair}');
const USER_PUBLIC_KEY = new PublicKey('${pdaData.metadata.user}');

// 🎯 WSOL-USDC PDA АДРЕСА (КАНОНИЧНЫЕ)
const WSOL_USDC_PDA = {
    // Основные PDA для add_liquidity2
    POSITION: new PublicKey('${pdaData.pdas.position.address}'),
    BITMAP_EXTENSION: new PublicKey('${pdaData.pdas.bitmapExtension.address}'),
    EVENT_AUTHORITY: new PublicKey('${pdaData.pdas.eventAuthority.address}'),
    
    // Дополнительные PDA
    STRATEGY: new PublicKey('${pdaData.pdas.strategy.address}'),
    LIQUIDITY_ACCOUNT: new PublicKey('${pdaData.pdas.liquidityAccount.address}'),
    GLOBAL_STATE: new PublicKey('${pdaData.pdas.globalState.address}'),
    
    // Метаданные
    BITMAP_BIN_ID: ${pdaData.pdas.bitmapExtension.binId},
    POSITION_BUMP: ${pdaData.pdas.position.bump},
    BITMAP_BUMP: ${pdaData.pdas.bitmapExtension.bump},
    EVENT_AUTHORITY_BUMP: ${pdaData.pdas.eventAuthority.bump}
};

// 🔧 ФУНКЦИИ ГЕНЕРАЦИИ (для динамического использования)
function getBinArrayBitmapExtensionPDA(lbPair, binId) {
    const BN = require('bn.js');
    const binIdBuffer = new BN(binId).toTwos(64).toArrayLike(Buffer, 'le', 8);
    return PublicKey.findProgramAddressSync(
        [Buffer.from("bin_array"), lbPair.toBuffer(), binIdBuffer],
        METEORA_DLMM_PROGRAM
    );
}

function getPositionPDA(lbPair, user) {
    return PublicKey.findProgramAddressSync(
        [Buffer.from("position"), lbPair.toBuffer(), user.toBuffer()],
        METEORA_DLMM_PROGRAM
    );
}

module.exports = {
    METEORA_DLMM_PROGRAM,
    WSOL_USDC_LB_PAIR,
    USER_PUBLIC_KEY,
    WSOL_USDC_PDA,
    getBinArrayBitmapExtensionPDA,
    getPositionPDA
};
`;

        const jsPath = path.join(outDir, 'wsol-usdc-pda-constants.js');
        fs.writeFileSync(jsPath, jsContent);
        console.log(`✅ JavaScript константы: ${jsPath}`);

        // 3. Интеграционный код для complete-flash-loan-structure.js
        const integrationContent = `/**
 * 🔧 КОД ДЛЯ ИНТЕГРАЦИИ В complete-flash-loan-structure.js
 * Замените существующие функции на эти каноничные версии
 */

// 🎯 КАНОНИЧНАЯ ФУНКЦИЯ getBinArrayBitmapExtension (ИСПРАВЛЕННАЯ!)
async getBinArrayBitmapExtension(poolAddress, binId = ${pdaData.pdas.bitmapExtension.binId}) {
    try {
        console.log(\`🔧 getBinArrayBitmapExtension: \${poolAddress.toString().slice(0,8)}..., binId=\${binId}\`);

        // 🔧 КАНОНИЧНОЕ КОДИРОВАНИЕ: BN.js с toTwos для signed i64 LE
        const binIdBuffer = new BN(binId).toTwos(64).toArrayLike(Buffer, 'le', 8);
        const seeds = [
            Buffer.from("bin_array"),           // Seed 1: строка "bin_array"
            poolAddress.toBuffer(),             // Seed 2: LB Pair address (32 байта)
            binIdBuffer                         // Seed 3: Bin ID как signed i64 LE (BN.js)
        ];

        const [pda, bump] = PublicKey.findProgramAddressSync(seeds, this.METEORA_DLMM_PROGRAM);

        console.log(\`   ✅ Bitmap Extension PDA (каноничная формула): \${pda.toString()}\`);
        console.log(\`   ✅ Bump: \${bump}\`);
        
        return pda;

    } catch (error) {
        console.error(\`❌ КРИТИЧЕСКАЯ ОШИБКА в getBinArrayBitmapExtension: \${error.message}\`);
        throw error;
    }
}

// 🎯 КАНОНИЧНАЯ ФУНКЦИЯ getPositionPDA
getPositionPDA(lbPair, user) {
    return PublicKey.findProgramAddressSync(
        [Buffer.from("position"), lbPair.toBuffer(), user.toBuffer()],
        this.METEORA_DLMM_PROGRAM
    );
}

// 🔧 ИЗВЕСТНЫЕ BIN ID ДЛЯ ПУЛОВ
const KNOWN_BIN_IDS = {
    '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6': ${pdaData.pdas.bitmapExtension.binId}, // WSOL-USDC
    'E9kQAzGFvgC7xtgNGCe63meEKBFw2T5yNjzgbJAtNcSJ': -8  // Kai-WSOL (из SDK перехвата)
};
`;

        const integrationPath = path.join(outDir, 'integration-code.js');
        fs.writeFileSync(integrationPath, integrationContent);
        console.log(`✅ Код для интеграции: ${integrationPath}`);

        return { jsonPath, jsPath, integrationPath };
    }
}

// Запуск генерации
function main() {
    try {
        const generator = new WSOLUSDCPDAGenerator();
        const pdaData = generator.generateAllPDAs();
        const exportPaths = generator.exportResults(pdaData);

        console.log('\n🎯 ИТОГОВАЯ СВОДКА:');
        console.log('==========================================');
        
        console.log(`📊 Position PDA: ${pdaData.metadata.positionMatches ? '✅ Совпадает' : '❌ Не совпадает'}`);
        console.log(`📊 Bitmap Extension: ${pdaData.metadata.bitmapMatches ? '✅ Совпадает' : '❌ Не совпадает'}`);
        console.log(`📁 Файлы экспорта: 3 файла созданы`);
        console.log(`🔧 Bin ID для Bitmap Extension: ${pdaData.pdas.bitmapExtension.binId}`);

        if (pdaData.metadata.bitmapMatches) {
            console.log(`\n🎉 BITMAP EXTENSION СОВПАДАЕТ!`);
            console.log(`   ✅ Ошибка 3007 должна быть исправлена!`);
            console.log(`   ✅ Готово к интеграции в основной код`);
        } else {
            console.log(`\n⚠️ BITMAP EXTENSION НЕ СОВПАДАЕТ`);
            console.log(`   🔧 Нужно найти правильный bin ID или использовать fallback`);
        }

        console.log(`\n📋 СЛЕДУЮЩИЕ ШАГИ:`);
        console.log(`1. Интегрировать код из integration-code.js`);
        console.log(`2. Использовать константы из wsol-usdc-pda-constants.js`);
        console.log(`3. Тестировать add_liquidity2 с новыми PDA`);

        process.exit(0);

    } catch (error) {
        console.error(`❌ КРИТИЧЕСКАЯ ОШИБКА:`, error.message);
        console.error(`📋 Stack trace:`, error.stack);
        process.exit(1);
    }
}

// Запуск если файл вызван напрямую
if (require.main === module) {
    main();
}

module.exports = WSOLUSDCPDAGenerator;
